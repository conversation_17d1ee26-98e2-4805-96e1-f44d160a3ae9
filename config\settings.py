from dotenv import load_dotenv
try:
    from pydantic import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

load_dotenv()

class Settings(BaseSettings):
    # API Keys
    OPENAI_API_KEY: str = "********************************************************************************************************************************************************************"
    ALPACA_API_KEY: str = "PK3CUPBGD0EK2S6GND10"
    ALPACA_SECRET_KEY: str = "xsybRgWQGNHpOZFJeeVQIb45tbwwEFUM53xQXyaO"
    FMP_API_KEY: str = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    
    # Alpaca Settings
    ALPACA_BASE_URL: str = "https://paper-api.alpaca.markets"  # Paper trading
    ALPACA_DATA_URL: str = "https://data.alpaca.markets"
    
    # Trading Settings
    MAX_POSITION_SIZE: float = 1000.0  # Maximum position size in USD
    RISK_PER_TRADE: float = 0.02  # 2% risk per trade
    MAX_DAILY_TRADES: int = 10
    MIN_VOLUME: int = 100000  # Minimum daily volume
    MIN_PRICE: float = 5.0  # Minimum stock price
    MAX_PRICE: float = 500.0  # Maximum stock price
    
    # Pattern Detection Settings
    RSI_OVERSOLD: float = 30.0
    RSI_OVERBOUGHT: float = 70.0
    VOLUME_SPIKE_MULTIPLIER: float = 2.0
    BREAKOUT_THRESHOLD: float = 0.05  # 5% breakout
    
    # Notification Settings
    ENABLE_VOICE_ALERTS: bool = True
    ENABLE_DESKTOP_NOTIFICATIONS: bool = True
    VOICE_RATE: int = 200  # Words per minute
    VOICE_VOLUME: float = 0.9
    
    # Database
    DATABASE_URL: str = "sqlite:///./trading_bot.db"
    
    # Scanning Settings
    SCAN_INTERVAL: int = 30  # seconds
    MARKET_OPEN_HOUR: int = 9
    MARKET_CLOSE_HOUR: int = 16
    TIMEZONE: str = "US/Eastern"
    
    # Watchlist
    WATCHLIST_SYMBOLS: list = [
        "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "AMD", 
        "NFLX", "CRM", "ADBE", "PYPL", "INTC", "CSCO", "ORCL", "IBM",
        "SPY", "QQQ", "IWM", "DIA"
    ]
    
    # AI Settings
    OPENAI_MODEL: str = "gpt-4"
    MAX_TOKENS: int = 1000
    TEMPERATURE: float = 0.7
    
    class Config:
        env_file = ".env"

settings = Settings()
