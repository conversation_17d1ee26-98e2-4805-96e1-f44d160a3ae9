from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from config.settings import settings

Base = declarative_base()

class Stock(Base):
    __tablename__ = "stocks"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, unique=True, index=True)
    name = Column(String)
    sector = Column(String)
    market_cap = Column(Float)
    price = Column(Float)
    volume = Column(Integer)
    avg_volume = Column(Integer)
    last_updated = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

class Trade(Base):
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, index=True)
    side = Column(String)  # BUY or SELL
    quantity = Column(Integer)
    price = Column(Float)
    total_value = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    strategy = Column(String)
    profit_loss = Column(Float, default=0.0)
    status = Column(String, default="PENDING")  # PENDING, FILLED, CANCELLED
    order_id = Column(String)

class Pattern(Base):
    __tablename__ = "patterns"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, index=True)
    pattern_type = Column(String)  # BREAKOUT, RSI_OVERSOLD, VOLUME_SPIKE, etc.
    confidence = Column(Float)
    detected_at = Column(DateTime, default=datetime.utcnow)
    price_at_detection = Column(Float)
    volume_at_detection = Column(Integer)
    description = Column(Text)
    action_taken = Column(String)  # TRADE, ALERT, IGNORE
    is_processed = Column(Boolean, default=False)

class Alert(Base):
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, index=True)
    alert_type = Column(String)  # PATTERN, TRADE, ERROR, INFO
    message = Column(Text)
    priority = Column(String, default="MEDIUM")  # LOW, MEDIUM, HIGH, CRITICAL
    timestamp = Column(DateTime, default=datetime.utcnow)
    is_sent = Column(Boolean, default=False)
    notification_methods = Column(String)  # VOICE, DESKTOP, EMAIL

class MarketData(Base):
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, index=True)
    timestamp = Column(DateTime, index=True)
    open_price = Column(Float)
    high_price = Column(Float)
    low_price = Column(Float)
    close_price = Column(Float)
    volume = Column(Integer)
    rsi = Column(Float)
    sma_20 = Column(Float)
    sma_50 = Column(Float)
    ema_12 = Column(Float)
    ema_26 = Column(Float)
    macd = Column(Float)
    signal_line = Column(Float)

# Database setup
engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
