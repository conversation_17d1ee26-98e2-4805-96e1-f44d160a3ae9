@echo off
echo ========================================
echo    STARTING AUTONOMOUS TRADING BOT
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not found! Please run setup.bat first.
    pause
    exit /b 1
)

echo Starting Trading Bot and Dashboard...
echo.
echo Dashboard will be available at: http://localhost:8050
echo.
echo Press Ctrl+C to stop the bot
echo.

python run_bot.py both

pause
