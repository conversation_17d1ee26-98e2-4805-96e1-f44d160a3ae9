#!/usr/bin/env python3
"""
StdDev/ATR Pattern Scanner
Specialized scanner for the custom volatility pattern
"""

import asyncio
import pandas as pd
import numpy as np
import ta
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from typing import Dict, List, Optional

# Import our modules
from config.settings import settings
from data.market_data import MarketDataProvider
from analysis.pattern_detector import PatternDetector
from notifications.alert_system import alert_system

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StdDevATRScanner:
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.market_data = MarketDataProvider()
        self.detected_patterns = []
        
    async def scan_symbol_for_stddev_atr(self, symbol: str) -> Optional[Dict]:
        """Scan a single symbol for StdDev/ATR pattern"""
        try:
            async with self.market_data as provider:
                # Get historical data
                hist_data = await provider.get_historical_data(symbol, "3mo")  # 3 months for better analysis
                
                if hist_data.empty or len(hist_data) < 20:
                    logger.warning(f"Insufficient data for {symbol}")
                    return None
                
                # Detect StdDev/ATR pattern specifically
                pattern = self.pattern_detector.detect_stddev_atr_pattern(hist_data, symbol)
                
                if pattern:
                    logger.info(f"StdDev/ATR pattern detected for {symbol}: {pattern['pattern_type']}")
                    return pattern
                    
        except Exception as e:
            logger.error(f"Error scanning {symbol}: {e}")
            
        return None
    
    async def scan_watchlist(self, symbols: List[str] = None) -> List[Dict]:
        """Scan watchlist for StdDev/ATR patterns"""
        if symbols is None:
            symbols = settings.WATCHLIST_SYMBOLS
        
        logger.info(f"Scanning {len(symbols)} symbols for StdDev/ATR patterns...")
        
        patterns = []
        for symbol in symbols:
            try:
                pattern = await self.scan_symbol_for_stddev_atr(symbol)
                if pattern:
                    patterns.append(pattern)
                    
                    # Send alert for brand new patterns
                    if pattern.get('is_brand_new', False):
                        await alert_system.send_stddev_atr_alert(pattern)
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                continue
        
        return patterns
    
    def create_stddev_atr_chart(self, symbol: str, df: pd.DataFrame, save_path: str = None):
        """Create the StdDev/ATR ratio chart"""
        try:
            # Calculate the pattern data
            std_dev_length = 20
            atr_length = 14
            
            # Calculate standard deviation
            df['std_dev'] = df['Close'].rolling(window=std_dev_length).std()
            
            # Calculate ATR
            atr_indicator = ta.volatility.AverageTrueRange(
                high=df['High'], 
                low=df['Low'], 
                close=df['Close'], 
                window=atr_length
            )
            df['atr'] = atr_indicator.average_true_range()
            
            # Calculate ratio
            df['ratio'] = (2 * df['std_dev']) / df['atr']
            
            # Create color mapping
            colors = np.where(
                df['ratio'] < 1, 'orange',
                np.where(df['ratio'] < 1.5, 'red',
                np.where(df['ratio'] < 2, 'gray',
                np.where(df['ratio'] > 4, 'yellow', 'green')))
            )
            
            # Create the plot
            plt.figure(figsize=(14, 8))
            
            # Main ratio plot
            plt.subplot(2, 1, 1)
            plt.plot(df.index, df['ratio'], label='2 × StdDev / ATR', linewidth=2, color='blue')
            plt.scatter(df.index, df['ratio'], c=colors, s=20, alpha=0.7)
            
            # Reference lines
            plt.axhline(y=1, color='orange', linestyle='--', linewidth=1.5, label='Low Volatility (< 1)')
            plt.axhline(y=2, color='gray', linestyle='--', linewidth=1.5, label='Normal Range (2)')
            plt.axhline(y=4, color='yellow', linestyle='--', linewidth=1.5, label='High Volatility (> 4)')
            
            plt.title(f'{symbol} - StdDev/ATR Volatility Pattern')
            plt.ylabel('Ratio')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Price chart
            plt.subplot(2, 1, 2)
            plt.plot(df.index, df['Close'], label='Close Price', linewidth=1.5, color='black')
            plt.title(f'{symbol} - Price Chart')
            plt.xlabel('Date')
            plt.ylabel('Price ($)')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"Chart saved to {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            logger.error(f"Error creating chart for {symbol}: {e}")
    
    async def continuous_scan(self, interval_minutes: int = 5):
        """Continuously scan for new StdDev/ATR patterns"""
        logger.info(f"Starting continuous StdDev/ATR pattern scanning (every {interval_minutes} minutes)")
        
        while True:
            try:
                logger.info("🔍 Scanning for StdDev/ATR patterns...")
                
                patterns = await self.scan_watchlist()
                
                if patterns:
                    logger.info(f"Found {len(patterns)} StdDev/ATR patterns:")
                    for pattern in patterns:
                        symbol = pattern['symbol']
                        ratio = pattern['ratio']
                        pattern_type = pattern['pattern_type']
                        action = pattern['action']
                        
                        print(f"  📊 {symbol}: {pattern_type} (Ratio: {ratio:.3f}) - {action}")
                        
                        # Special alert for extreme patterns
                        if ratio < 1 or ratio > 4:
                            print(f"  🚨 EXTREME PATTERN: {symbol} ratio {ratio:.3f}")
                else:
                    logger.info("No significant StdDev/ATR patterns detected")
                
                # Wait for next scan
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in continuous scan: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error

async def main():
    """Main function for testing"""
    scanner = StdDevATRScanner()
    
    print("🤖 StdDev/ATR Pattern Scanner")
    print("=" * 50)
    
    # Test with a few symbols
    test_symbols = ["AAPL", "TSLA", "MSFT", "GOOGL", "AMZN"]
    
    print(f"Testing StdDev/ATR pattern detection on: {', '.join(test_symbols)}")
    print()
    
    patterns = await scanner.scan_watchlist(test_symbols)
    
    if patterns:
        print(f"✅ Found {len(patterns)} StdDev/ATR patterns:")
        print()
        
        for pattern in patterns:
            symbol = pattern['symbol']
            ratio = pattern['ratio']
            prev_ratio = pattern.get('prev_ratio', 0)
            pattern_type = pattern['pattern_type']
            action = pattern['action']
            confidence = pattern['confidence']
            description = pattern['description']
            
            print(f"📊 {symbol}:")
            print(f"   Pattern: {pattern_type}")
            print(f"   Ratio: {ratio:.3f} (Previous: {prev_ratio:.3f})")
            print(f"   Action: {action}")
            print(f"   Confidence: {confidence*100:.0f}%")
            print(f"   Description: {description}")
            print()
            
            # Create chart for extreme patterns
            if ratio < 1 or ratio > 4:
                print(f"🎨 Creating chart for {symbol} (extreme pattern)...")
                try:
                    async with scanner.market_data as provider:
                        hist_data = await provider.get_historical_data(symbol, "3mo")
                        if not hist_data.empty:
                            scanner.create_stddev_atr_chart(symbol, hist_data, f"{symbol}_stddev_atr.png")
                except Exception as e:
                    print(f"Error creating chart: {e}")
    else:
        print("❌ No StdDev/ATR patterns detected in test symbols")
    
    print("\n🔄 Starting continuous scanning...")
    print("Press Ctrl+C to stop")
    
    try:
        await scanner.continuous_scan(interval_minutes=2)  # Scan every 2 minutes for testing
    except KeyboardInterrupt:
        print("\n⏹️ Scanning stopped by user")

if __name__ == "__main__":
    asyncio.run(main())
