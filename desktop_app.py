#!/usr/bin/env python3
"""
Autonomous Trading Bot - Desktop Application
A modern desktop GUI for the trading bot system.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import customtkinter as ctk
import threading
import asyncio
import queue
import time
from datetime import datetime, timedelta
import logging
import sys
import json
from typing import Dict, List, Optional

# Import our modules
from config.settings import settings
from database.models import create_tables
from data.market_data import MarketDataProvider
from analysis.pattern_detector import PatternDetector
from trading.broker_interface import AlpacaBroker
from notifications.alert_system import alert_system
from ai.openai_integration import ai_analyst

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class TradingBotGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🤖 Autonomous Trading Bot")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Bot components
        self.market_data = None
        self.pattern_detector = PatternDetector()
        self.broker = AlpacaBroker()
        self.is_running = False
        self.bot_thread = None
        
        # GUI state
        self.message_queue = queue.Queue()
        self.patterns_data = []
        self.trades_data = []
        self.positions_data = []
        self.account_data = {}
        
        # Create GUI
        self.create_widgets()
        self.setup_layout()
        
        # Start message processor
        self.process_messages()
        
        # Initialize system
        self.initialize_system()

    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🤖 Autonomous Trading Bot", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        
        # Status frame
        self.status_frame = ctk.CTkFrame(self.main_frame)
        
        # Account info cards
        self.account_frame = ctk.CTkFrame(self.main_frame)
        
        self.balance_label = ctk.CTkLabel(self.account_frame, text="Account Balance", font=ctk.CTkFont(size=12))
        self.balance_value = ctk.CTkLabel(self.account_frame, text="$0.00", font=ctk.CTkFont(size=20, weight="bold"))
        
        self.pnl_label = ctk.CTkLabel(self.account_frame, text="Daily P&L", font=ctk.CTkFont(size=12))
        self.pnl_value = ctk.CTkLabel(self.account_frame, text="$0.00", font=ctk.CTkFont(size=20, weight="bold"))
        
        self.positions_label = ctk.CTkLabel(self.account_frame, text="Positions", font=ctk.CTkFont(size=12))
        self.positions_value = ctk.CTkLabel(self.account_frame, text="0", font=ctk.CTkFont(size=20, weight="bold"))
        
        self.patterns_label = ctk.CTkLabel(self.account_frame, text="Patterns Today", font=ctk.CTkFont(size=12))
        self.patterns_value = ctk.CTkLabel(self.account_frame, text="0", font=ctk.CTkFont(size=20, weight="bold"))
        
        # Control buttons
        self.control_frame = ctk.CTkFrame(self.main_frame)
        
        self.start_button = ctk.CTkButton(
            self.control_frame, 
            text="🚀 Start Bot", 
            command=self.start_bot,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        
        self.stop_button = ctk.CTkButton(
            self.control_frame, 
            text="⏹️ Stop Bot", 
            command=self.stop_bot,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            state="disabled"
        )
        
        self.test_button = ctk.CTkButton(
            self.control_frame,
            text="🔧 Test System",
            command=self.test_system,
            font=ctk.CTkFont(size=14),
            height=40
        )

        self.stddev_scan_button = ctk.CTkButton(
            self.control_frame,
            text="📊 Scan StdDev/ATR",
            command=self.scan_stddev_atr,
            font=ctk.CTkFont(size=14),
            height=40
        )
        
        # Status indicator
        self.status_label = ctk.CTkLabel(
            self.control_frame, 
            text="Status: Stopped", 
            font=ctk.CTkFont(size=14, weight="bold")
        )
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        
        # Patterns tab
        self.patterns_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.patterns_frame, text="📊 Patterns")
        
        self.patterns_tree = ttk.Treeview(
            self.patterns_frame,
            columns=("Time", "Symbol", "Pattern", "Confidence", "Action", "Price"),
            show="headings",
            height=10
        )
        
        # Configure pattern tree columns
        for col in ("Time", "Symbol", "Pattern", "Confidence", "Action", "Price"):
            self.patterns_tree.heading(col, text=col)
            self.patterns_tree.column(col, width=120)
        
        self.patterns_scroll = ttk.Scrollbar(self.patterns_frame, orient="vertical", command=self.patterns_tree.yview)
        self.patterns_tree.configure(yscrollcommand=self.patterns_scroll.set)
        
        # Trades tab
        self.trades_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.trades_frame, text="💰 Trades")
        
        self.trades_tree = ttk.Treeview(
            self.trades_frame,
            columns=("Time", "Symbol", "Side", "Quantity", "Price", "Status", "P&L"),
            show="headings",
            height=10
        )
        
        # Configure trades tree columns
        for col in ("Time", "Symbol", "Side", "Quantity", "Price", "Status", "P&L"):
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=100)
        
        self.trades_scroll = ttk.Scrollbar(self.trades_frame, orient="vertical", command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=self.trades_scroll.set)
        
        # Positions tab
        self.positions_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.positions_frame, text="📈 Positions")
        
        self.positions_tree = ttk.Treeview(
            self.positions_frame,
            columns=("Symbol", "Quantity", "Avg Price", "Current Price", "P&L", "P&L %"),
            show="headings",
            height=10
        )
        
        # Configure positions tree columns
        for col in ("Symbol", "Quantity", "Avg Price", "Current Price", "P&L", "P&L %"):
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=120)
        
        self.positions_scroll = ttk.Scrollbar(self.positions_frame, orient="vertical", command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=self.positions_scroll.set)
        
        # Log tab
        self.log_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 Logs")
        
        self.log_text = scrolledtext.ScrolledText(
            self.log_frame,
            height=15,
            bg="#2b2b2b",
            fg="#ffffff",
            font=("Consolas", 10)
        )
        
        # Settings tab
        self.settings_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.settings_frame, text="⚙️ Settings")
        
        self.create_settings_widgets()

    def create_settings_widgets(self):
        """Create settings widgets"""
        
        # Trading settings
        trading_label = ctk.CTkLabel(self.settings_frame, text="Trading Settings", font=ctk.CTkFont(size=16, weight="bold"))
        
        self.max_position_var = tk.StringVar(value=str(settings.MAX_POSITION_SIZE))
        max_position_label = ctk.CTkLabel(self.settings_frame, text="Max Position Size ($):")
        self.max_position_entry = ctk.CTkEntry(self.settings_frame, textvariable=self.max_position_var)
        
        self.risk_per_trade_var = tk.StringVar(value=str(settings.RISK_PER_TRADE * 100))
        risk_label = ctk.CTkLabel(self.settings_frame, text="Risk Per Trade (%):")
        self.risk_entry = ctk.CTkEntry(self.settings_frame, textvariable=self.risk_per_trade_var)
        
        self.max_trades_var = tk.StringVar(value=str(settings.MAX_DAILY_TRADES))
        max_trades_label = ctk.CTkLabel(self.settings_frame, text="Max Daily Trades:")
        self.max_trades_entry = ctk.CTkEntry(self.settings_frame, textvariable=self.max_trades_var)
        
        # Notification settings
        notification_label = ctk.CTkLabel(self.settings_frame, text="Notification Settings", font=ctk.CTkFont(size=16, weight="bold"))
        
        self.voice_alerts_var = tk.BooleanVar(value=settings.ENABLE_VOICE_ALERTS)
        self.voice_alerts_check = ctk.CTkCheckBox(self.settings_frame, text="Enable Voice Alerts", variable=self.voice_alerts_var)
        
        self.desktop_notifications_var = tk.BooleanVar(value=settings.ENABLE_DESKTOP_NOTIFICATIONS)
        self.desktop_notifications_check = ctk.CTkCheckBox(self.settings_frame, text="Enable Desktop Notifications", variable=self.desktop_notifications_var)
        
        # Save button
        self.save_settings_button = ctk.CTkButton(
            self.settings_frame,
            text="💾 Save Settings",
            command=self.save_settings
        )
        
        # Pack settings widgets
        trading_label.pack(pady=(20, 10))
        max_position_label.pack(pady=5)
        self.max_position_entry.pack(pady=5)
        risk_label.pack(pady=5)
        self.risk_entry.pack(pady=5)
        max_trades_label.pack(pady=5)
        self.max_trades_entry.pack(pady=5)
        
        notification_label.pack(pady=(20, 10))
        self.voice_alerts_check.pack(pady=5)
        self.desktop_notifications_check.pack(pady=5)
        
        self.save_settings_button.pack(pady=20)

    def setup_layout(self):
        """Setup the layout of widgets"""
        
        # Title
        self.title_label.pack(pady=(0, 20))
        
        # Account info
        self.account_frame.pack(fill="x", pady=(0, 10))
        
        # Create grid for account info
        self.balance_label.grid(row=0, column=0, padx=20, pady=5)
        self.balance_value.grid(row=1, column=0, padx=20, pady=5)
        
        self.pnl_label.grid(row=0, column=1, padx=20, pady=5)
        self.pnl_value.grid(row=1, column=1, padx=20, pady=5)
        
        self.positions_label.grid(row=0, column=2, padx=20, pady=5)
        self.positions_value.grid(row=1, column=2, padx=20, pady=5)
        
        self.patterns_label.grid(row=0, column=3, padx=20, pady=5)
        self.patterns_value.grid(row=1, column=3, padx=20, pady=5)
        
        # Control buttons
        self.control_frame.pack(fill="x", pady=(0, 10))
        
        self.start_button.pack(side="left", padx=10)
        self.stop_button.pack(side="left", padx=10)
        self.test_button.pack(side="left", padx=10)
        self.stddev_scan_button.pack(side="left", padx=10)
        self.status_label.pack(side="right", padx=10)
        
        # Notebook
        self.notebook.pack(fill="both", expand=True)
        
        # Pack tree views
        self.patterns_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        self.patterns_scroll.pack(side="right", fill="y", pady=10)
        
        self.trades_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        self.trades_scroll.pack(side="right", fill="y", pady=10)
        
        self.positions_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        self.positions_scroll.pack(side="right", fill="y", pady=10)
        
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)

    def log_message(self, message: str, level: str = "INFO"):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.message_queue.put(("log", log_entry))
        
        # Also log to file
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        else:
            logger.info(message)

    def process_messages(self):
        """Process messages from queue"""
        try:
            while True:
                msg_type, data = self.message_queue.get_nowait()
                
                if msg_type == "log":
                    self.log_text.insert(tk.END, data)
                    self.log_text.see(tk.END)
                elif msg_type == "pattern":
                    self.add_pattern_to_tree(data)
                elif msg_type == "trade":
                    self.add_trade_to_tree(data)
                elif msg_type == "account":
                    self.update_account_display(data)
                elif msg_type == "status":
                    self.update_status(data)
                    
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_messages)

    def add_pattern_to_tree(self, pattern: Dict):
        """Add pattern to patterns tree"""
        pattern_type = pattern.get('pattern_type', '')

        # Special formatting for StdDev/ATR patterns
        if "STDDEV_ATR" in pattern_type:
            ratio = pattern.get('ratio', 0)
            pattern_display = f"{pattern_type} (R:{ratio:.2f})"
        else:
            pattern_display = pattern_type

        values = (
            datetime.now().strftime("%H:%M:%S"),
            pattern.get('symbol', ''),
            pattern_display,
            f"{pattern.get('confidence', 0)*100:.0f}%",
            pattern.get('action', ''),
            f"${pattern.get('price', 0):.2f}"
        )

        item = self.patterns_tree.insert("", 0, values=values)

        # Highlight StdDev/ATR patterns
        if "STDDEV_ATR" in pattern_type:
            if "ORANGE" in pattern_type or "YELLOW" in pattern_type:
                # These are the important ones - make them stand out
                self.patterns_tree.set(item, "Symbol", f"🚨 {pattern.get('symbol', '')}")

        # Keep only last 100 entries
        children = self.patterns_tree.get_children()
        if len(children) > 100:
            self.patterns_tree.delete(children[-1])

        # Show chat-style alert for StdDev/ATR patterns
        if "STDDEV_ATR" in pattern_type and pattern.get('is_brand_new', False):
            self.show_chat_alert(pattern)

    def show_chat_alert(self, pattern: Dict):
        """Show chat-style alert for StdDev/ATR patterns"""
        try:
            symbol = pattern.get('symbol', 'Unknown')
            ratio = pattern.get('ratio', 0)
            price = pattern.get('price', 0)
            action = pattern.get('action', 'WATCH')
            pattern_type = pattern.get('pattern_type', '')

            # Create chat message
            if "ORANGE" in pattern_type:
                emoji = "🟠"
                alert_type = "LOW VOLATILITY BREAKOUT SETUP"
            elif "YELLOW" in pattern_type:
                emoji = "🟡"
                alert_type = "EXTREME VOLATILITY REVERSAL"
            elif "RED" in pattern_type:
                emoji = "🔴"
                alert_type = "MODERATE VOLATILITY"
            elif "GREEN" in pattern_type:
                emoji = "🟢"
                alert_type = "ELEVATED VOLATILITY"
            else:
                emoji = "⚫"
                alert_type = "NORMAL VOLATILITY"

            chat_message = f"""
{emoji} STDDEV/ATR ALERT: {symbol}
Ratio: {ratio:.3f} | Price: ${price:.2f}
Action: {action} | Type: {alert_type}
            """.strip()

            # Add to log with special formatting
            log_message = f"\n{'='*50}\n🤖 CHAT ALERT\n{'='*50}\n{chat_message}\n{'='*50}\n"
            self.message_queue.put(("log", log_message))

            # Show popup for extreme cases
            if ratio < 1 or ratio > 4:
                messagebox.showinfo(f"🚨 {alert_type}", chat_message)

        except Exception as e:
            self.log_message(f"Error showing chat alert: {e}", "ERROR")

    def scan_stddev_atr(self):
        """Scan specifically for StdDev/ATR patterns"""
        def scan_async():
            try:
                self.log_message("🔍 Starting StdDev/ATR pattern scan...")

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Import the scanner
                from stddev_atr_scanner import StdDevATRScanner
                scanner = StdDevATRScanner()

                # Scan watchlist
                patterns = loop.run_until_complete(scanner.scan_watchlist(settings.WATCHLIST_SYMBOLS[:10]))

                if patterns:
                    self.log_message(f"✅ Found {len(patterns)} StdDev/ATR patterns!")

                    for pattern in patterns:
                        symbol = pattern['symbol']
                        ratio = pattern['ratio']
                        pattern_type = pattern['pattern_type']
                        action = pattern['action']

                        # Add to patterns tree
                        self.message_queue.put(("pattern", pattern))

                        # Log detailed info
                        self.log_message(f"📊 {symbol}: {pattern_type} (Ratio: {ratio:.3f}) - {action}")

                        # Special handling for extreme patterns
                        if ratio < 1:
                            self.log_message(f"🟠 LOW VOLATILITY ALERT: {symbol} ratio {ratio:.3f} - Potential breakout!", "WARNING")
                        elif ratio > 4:
                            self.log_message(f"🟡 EXTREME VOLATILITY ALERT: {symbol} ratio {ratio:.3f} - Potential reversal!", "WARNING")
                else:
                    self.log_message("❌ No significant StdDev/ATR patterns detected")

                loop.close()

            except Exception as e:
                self.log_message(f"Error in StdDev/ATR scan: {e}", "ERROR")

        # Run scan in background
        scan_thread = threading.Thread(target=scan_async, daemon=True)
        scan_thread.start()

    def add_trade_to_tree(self, trade: Dict):
        """Add trade to trades tree"""
        values = (
            datetime.now().strftime("%H:%M:%S"),
            trade.get('symbol', ''),
            trade.get('side', ''),
            trade.get('quantity', 0),
            f"${trade.get('filled_avg_price', 0):.2f}",
            trade.get('status', ''),
            f"${trade.get('profit_loss', 0):.2f}"
        )
        
        self.trades_tree.insert("", 0, values=values)

    def update_account_display(self, account: Dict):
        """Update account information display"""
        self.balance_value.configure(text=f"${account.get('buying_power', 0):,.2f}")
        
        # Calculate daily P&L (simplified)
        daily_pnl = 0  # Would calculate from trades
        color = "green" if daily_pnl >= 0 else "red"
        self.pnl_value.configure(text=f"${daily_pnl:,.2f}", text_color=color)
        
        # Update positions count
        positions_count = len(account.get('positions', []))
        self.positions_value.configure(text=str(positions_count))

    def update_status(self, status: str):
        """Update bot status"""
        self.status_label.configure(text=f"Status: {status}")
        
        if status == "Running":
            self.status_label.configure(text_color="green")
        elif status == "Stopped":
            self.status_label.configure(text_color="red")
        else:
            self.status_label.configure(text_color="orange")

    def initialize_system(self):
        """Initialize the trading system"""
        def init_async():
            try:
                # Create database tables
                create_tables()
                self.log_message("Database initialized")
                
                # Initialize broker
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                success = loop.run_until_complete(self.broker.initialize())
                if success:
                    self.log_message("Broker connection established")
                    account = self.broker.get_account_info()
                    self.message_queue.put(("account", account))
                else:
                    self.log_message("Failed to connect to broker", "ERROR")
                
                loop.close()
                
            except Exception as e:
                self.log_message(f"Initialization error: {e}", "ERROR")
        
        # Run initialization in background
        init_thread = threading.Thread(target=init_async, daemon=True)
        init_thread.start()

    def start_bot(self):
        """Start the trading bot"""
        if self.is_running:
            return
        
        self.is_running = True
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        
        self.message_queue.put(("status", "Starting"))
        self.log_message("Starting trading bot...")
        
        # Start bot in background thread
        self.bot_thread = threading.Thread(target=self.run_bot_async, daemon=True)
        self.bot_thread.start()

    def stop_bot(self):
        """Stop the trading bot"""
        self.is_running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        
        self.message_queue.put(("status", "Stopped"))
        self.log_message("Trading bot stopped")

    def run_bot_async(self):
        """Run the bot asynchronously"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            self.message_queue.put(("status", "Running"))
            self.log_message("Bot is now running and scanning markets...")
            
            loop.run_until_complete(self.bot_main_loop())
            
        except Exception as e:
            self.log_message(f"Bot error: {e}", "ERROR")
        finally:
            self.message_queue.put(("status", "Stopped"))

    async def bot_main_loop(self):
        """Main bot loop"""
        self.market_data = MarketDataProvider()
        
        while self.is_running:
            try:
                # Scan market for patterns
                async with self.market_data as provider:
                    # Get market data
                    watchlist_data = await provider.scan_for_patterns(settings.WATCHLIST_SYMBOLS[:5])
                    
                    for quote in watchlist_data:
                        if not self.is_running:
                            break
                            
                        symbol = quote.get('symbol')
                        if symbol:
                            # Get historical data
                            hist_data = await provider.get_historical_data(symbol, "1mo")
                            
                            if not hist_data.empty:
                                # Detect patterns
                                patterns = await self.pattern_detector.analyze_symbol(hist_data, symbol)
                                
                                for pattern in patterns:
                                    self.message_queue.put(("pattern", pattern))
                                    
                                    # Send notification
                                    await alert_system.send_pattern_alert(pattern)
                                    
                                    # Check if we should trade
                                    if pattern.get('confidence', 0) > 0.8 and pattern.get('action') in ['BUY', 'SELL']:
                                        self.log_message(f"High confidence pattern detected: {symbol} - {pattern['pattern_type']}")
                
                # Update account info
                account = self.broker.get_account_info()
                if account:
                    self.message_queue.put(("account", account))
                
                # Wait before next scan
                await asyncio.sleep(settings.SCAN_INTERVAL)
                
            except Exception as e:
                self.log_message(f"Scan error: {e}", "ERROR")
                await asyncio.sleep(60)  # Wait longer on error

    def test_system(self):
        """Test system connections"""
        def test_async():
            try:
                self.log_message("Testing system connections...")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Test broker
                success = loop.run_until_complete(self.broker.initialize())
                if success:
                    self.log_message("✓ Broker connection successful")
                else:
                    self.log_message("✗ Broker connection failed", "ERROR")
                
                # Test market data
                async def test_market_data():
                    async with MarketDataProvider() as provider:
                        quote = await provider.get_real_time_quote("AAPL")
                        if quote:
                            self.log_message("✓ Market data connection successful")
                            return True
                        else:
                            self.log_message("✗ Market data connection failed", "ERROR")
                            return False
                
                market_success = loop.run_until_complete(test_market_data())
                
                # Test AI
                async def test_ai():
                    try:
                        response = await ai_analyst._make_openai_request("Test connection. Respond with 'OK'.")
                        if "OK" in response or "ok" in response.lower():
                            self.log_message("✓ AI connection successful")
                            return True
                        else:
                            self.log_message("✗ AI connection failed", "ERROR")
                            return False
                    except Exception as e:
                        self.log_message(f"✗ AI connection failed: {e}", "ERROR")
                        return False
                
                ai_success = loop.run_until_complete(test_ai())
                
                if success and market_success and ai_success:
                    self.log_message("🎉 All systems operational!")
                    messagebox.showinfo("System Test", "All systems are operational!")
                else:
                    self.log_message("⚠️ Some systems failed tests", "WARNING")
                    messagebox.showwarning("System Test", "Some systems failed. Check logs for details.")
                
                loop.close()
                
            except Exception as e:
                self.log_message(f"Test error: {e}", "ERROR")
                messagebox.showerror("System Test", f"Test failed: {e}")
        
        # Run test in background
        test_thread = threading.Thread(target=test_async, daemon=True)
        test_thread.start()

    def save_settings(self):
        """Save settings"""
        try:
            # Update settings (in a real app, you'd save to config file)
            settings.MAX_POSITION_SIZE = float(self.max_position_var.get())
            settings.RISK_PER_TRADE = float(self.risk_per_trade_var.get()) / 100
            settings.MAX_DAILY_TRADES = int(self.max_trades_var.get())
            settings.ENABLE_VOICE_ALERTS = self.voice_alerts_var.get()
            settings.ENABLE_DESKTOP_NOTIFICATIONS = self.desktop_notifications_var.get()
            
            self.log_message("Settings saved successfully")
            messagebox.showinfo("Settings", "Settings saved successfully!")
            
        except Exception as e:
            self.log_message(f"Error saving settings: {e}", "ERROR")
            messagebox.showerror("Settings", f"Error saving settings: {e}")

    def on_closing(self):
        """Handle window closing"""
        if self.is_running:
            if messagebox.askokcancel("Quit", "Bot is running. Stop bot and quit?"):
                self.stop_bot()
                time.sleep(1)  # Give time for bot to stop
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Run the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("Trading Bot Desktop Application started")
        self.log_message("Click 'Test System' to verify connections, then 'Start Bot' to begin trading")
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = TradingBotGUI()
        app.run()
    except Exception as e:
        logger.error(f"Application error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")

if __name__ == "__main__":
    main()
