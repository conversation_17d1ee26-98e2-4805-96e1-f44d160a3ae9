# 🐍 Python Installation Guide

## Step 1: Download Python

1. **Go to**: https://www.python.org/downloads/
2. **Click**: "Download Python 3.11.x" (latest version)
3. **Save** the installer to your Downloads folder

## Step 2: Install Python

1. **Run** the downloaded installer (python-3.11.x-amd64.exe)
2. **⚠️ IMPORTANT**: Check the box "Add Python to PATH" at the bottom
3. **Click**: "Install Now"
4. **Wait** for installation to complete
5. **Click**: "Close"

## Step 3: Verify Installation

1. **Open** Command Prompt (Windows Key + R, type "cmd", press Enter)
2. **Type**: `python --version`
3. **Press** Enter
4. **You should see**: `Python 3.11.x`

If you see "Python was not found", restart your computer and try again.

## Step 4: Run the Trading Bot

Once Python is installed:

### Option 1: Simple Launcher
1. **Double-click**: `launcher.py`
2. **Click**: "Install Dependencies"
3. **Click**: "Test System"
4. **Click**: "Launch Trading Bot"

### Option 2: Command Line
1. **Open** Command Prompt in the Stockbot folder
2. **Run**: `python -m pip install -r requirements.txt`
3. **Run**: `python test_system.py`
4. **Run**: `python desktop_app.py`

### Option 3: Batch Files
1. **Double-click**: `setup.bat`
2. **Double-click**: `start_desktop.bat`

## Troubleshooting

### "Python was not found"
- Make sure you checked "Add Python to PATH" during installation
- Restart your computer
- Try using `py` instead of `python`

### "Permission denied"
- Right-click Command Prompt and "Run as Administrator"
- Try installing with: `python -m pip install --user -r requirements.txt`

### "Module not found"
- Run: `python -m pip install --upgrade pip`
- Run: `python -m pip install -r requirements.txt`

## What Happens Next

Once Python is installed and you run the desktop app:

1. **Modern GUI Opens** - Professional trading interface
2. **System Tests** - Verifies all API connections
3. **Bot Starts** - Begins scanning markets automatically
4. **Real-time Alerts** - Voice and visual notifications
5. **Live Trading** - Executes trades based on AI analysis

## Your API Keys (Already Configured)

✅ **OpenAI**: Ready for AI analysis  
✅ **Alpaca**: Ready for paper trading  
✅ **FMP**: Ready for market data  

## Safety Features

- **Paper Trading Only** - No real money at risk
- **Position Limits** - Max $1,000 per trade
- **Risk Controls** - 2% risk per trade maximum
- **Daily Limits** - Max 10 trades per day

---

**Need Help?** 
- Check the logs in `trading_bot.log`
- Run `python test_system.py` to diagnose issues
- All your API keys are pre-configured and ready to use!
