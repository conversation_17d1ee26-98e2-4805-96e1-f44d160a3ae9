#!/usr/bin/env python3
"""
Professional Trading Bot
Real market analysis, technical indicators, and trading insights
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import threading
import time
from datetime import datetime, timedelta
import yfinance as yf
import pandas as pd
import numpy as np
import requests
import json

class ProfessionalTradingAnalyst:
    """Professional trading analysis with real market data"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
        
    def get_comprehensive_analysis(self, symbol):
        """Get comprehensive stock analysis"""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get stock info
            info = ticker.info
            hist = ticker.history(period="6mo")
            
            if hist.empty:
                return f"Unable to retrieve data for {symbol}"
            
            # Current price data
            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100
            
            # Technical indicators
            sma_20 = hist['Close'].rolling(20).mean().iloc[-1]
            sma_50 = hist['Close'].rolling(50).mean().iloc[-1]
            rsi = self.calculate_rsi(hist['Close'])
            
            # Volume analysis
            avg_volume = hist['Volume'].rolling(20).mean().iloc[-1]
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume
            
            # Support/Resistance
            high_52w = info.get('fiftyTwoWeekHigh', hist['High'].max())
            low_52w = info.get('fiftyTwoWeekLow', hist['Low'].min())
            
            # Market cap and valuation
            market_cap = info.get('marketCap', 0)
            pe_ratio = info.get('trailingPE')
            
            analysis = f"""
📊 **{symbol} - {info.get('longName', symbol)}**

💰 **Price Action:**
• Current: ${current_price:.2f}
• Change: {'+' if change >= 0 else ''}${change:.2f} ({change_percent:+.2f}%)
• 52W Range: ${low_52w:.2f} - ${high_52w:.2f}

📈 **Technical Analysis:**
• SMA 20: ${sma_20:.2f} {'✅ Above' if current_price > sma_20 else '❌ Below'}
• SMA 50: ${sma_50:.2f} {'✅ Above' if current_price > sma_50 else '❌ Below'}
• RSI: {rsi:.1f} {'🔥 Overbought' if rsi > 70 else '💎 Oversold' if rsi < 30 else '⚖️ Neutral'}

📊 **Volume Analysis:**
• Current Volume: {current_volume:,.0f}
• Avg Volume: {avg_volume:,.0f}
• Volume Ratio: {volume_ratio:.2f}x {'🚀 High' if volume_ratio > 1.5 else '📉 Low' if volume_ratio < 0.5 else '➡️ Normal'}

🏢 **Fundamentals:**
• Market Cap: {self.format_market_cap(market_cap)}
• P/E Ratio: {pe_ratio:.2f if pe_ratio else 'N/A'}
• Sector: {info.get('sector', 'N/A')}

🎯 **Trading Signals:**
{self.generate_trading_signals(current_price, sma_20, sma_50, rsi, volume_ratio)}

⚠️ **Risk Assessment:**
{self.assess_risk(symbol, current_price, high_52w, low_52w, rsi, volume_ratio)}
            """.strip()
            
            return analysis
            
        except Exception as e:
            return f"Error analyzing {symbol}: {str(e)}"
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1]
    
    def generate_trading_signals(self, price, sma20, sma50, rsi, volume_ratio):
        """Generate trading signals based on technical analysis"""
        signals = []
        
        # Trend signals
        if price > sma20 > sma50:
            signals.append("🟢 BULLISH: Price above both moving averages")
        elif price < sma20 < sma50:
            signals.append("🔴 BEARISH: Price below both moving averages")
        else:
            signals.append("🟡 MIXED: Conflicting trend signals")
        
        # RSI signals
        if rsi > 70:
            signals.append("⚠️ OVERBOUGHT: Consider taking profits")
        elif rsi < 30:
            signals.append("💎 OVERSOLD: Potential buying opportunity")
        else:
            signals.append("⚖️ RSI NEUTRAL: No extreme conditions")
        
        # Volume signals
        if volume_ratio > 2:
            signals.append("🚀 HIGH VOLUME: Strong interest/momentum")
        elif volume_ratio < 0.5:
            signals.append("😴 LOW VOLUME: Weak conviction")
        
        return "\n".join([f"• {signal}" for signal in signals])
    
    def assess_risk(self, symbol, price, high_52w, low_52w, rsi, volume_ratio):
        """Assess trading risk"""
        risk_factors = []
        
        # Price position risk
        price_position = (price - low_52w) / (high_52w - low_52w)
        if price_position > 0.9:
            risk_factors.append("🔴 HIGH: Near 52-week high")
        elif price_position < 0.1:
            risk_factors.append("🟡 MODERATE: Near 52-week low")
        else:
            risk_factors.append("🟢 MODERATE: Mid-range position")
        
        # Volatility risk
        if rsi > 80 or rsi < 20:
            risk_factors.append("🔴 HIGH: Extreme RSI levels")
        
        # Volume risk
        if volume_ratio < 0.3:
            risk_factors.append("🟡 MODERATE: Very low volume")
        
        return "\n".join([f"• {risk}" for risk in risk_factors])
    
    def format_market_cap(self, market_cap):
        """Format market cap for display"""
        if market_cap >= 1e12:
            return f"${market_cap/1e12:.2f}T"
        elif market_cap >= 1e9:
            return f"${market_cap/1e9:.2f}B"
        elif market_cap >= 1e6:
            return f"${market_cap/1e6:.2f}M"
        else:
            return f"${market_cap:,.0f}"
    
    def get_market_overview(self):
        """Get overall market overview"""
        try:
            indices = {
                '^GSPC': 'S&P 500',
                '^IXIC': 'NASDAQ',
                '^DJI': 'Dow Jones'
            }
            
            overview = "📈 **MARKET OVERVIEW**\n\n"
            
            for symbol, name in indices.items():
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="2d")
                
                if not hist.empty:
                    current = hist['Close'].iloc[-1]
                    previous = hist['Close'].iloc[-2]
                    change = current - previous
                    change_percent = (change / previous) * 100
                    
                    overview += f"• **{name}**: {current:.2f} "
                    overview += f"({change:+.2f}, {change_percent:+.2f}%)\n"
            
            return overview
            
        except Exception as e:
            return f"Error getting market overview: {str(e)}"
    
    def get_sector_analysis(self, symbol):
        """Get sector performance analysis"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            sector = info.get('sector', 'Unknown')
            
            # Get sector ETF performance (simplified mapping)
            sector_etfs = {
                'Technology': 'XLK',
                'Healthcare': 'XLV',
                'Financial Services': 'XLF',
                'Consumer Cyclical': 'XLY',
                'Communication Services': 'XLC',
                'Industrial': 'XLI',
                'Consumer Defensive': 'XLP',
                'Energy': 'XLE',
                'Utilities': 'XLU',
                'Real Estate': 'XLRE',
                'Materials': 'XLB'
            }
            
            etf_symbol = sector_etfs.get(sector)
            if etf_symbol:
                etf_ticker = yf.Ticker(etf_symbol)
                etf_hist = etf_ticker.history(period="1mo")
                
                if not etf_hist.empty:
                    etf_change = ((etf_hist['Close'].iloc[-1] / etf_hist['Close'].iloc[0]) - 1) * 100
                    
                    return f"""
🏭 **SECTOR ANALYSIS - {sector}**
• Sector ETF ({etf_symbol}): {etf_change:+.2f}% (1 month)
• Sector Trend: {'🟢 Outperforming' if etf_change > 2 else '🔴 Underperforming' if etf_change < -2 else '🟡 Neutral'}
                    """.strip()
            
            return f"🏭 **SECTOR**: {sector} (No ETF data available)"
            
        except Exception as e:
            return f"Error getting sector analysis: {str(e)}"

class ProfessionalTradingGUI:
    """Professional trading interface"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Professional Trading Bot - Real Market Analysis")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        self.analyst = ProfessionalTradingAnalyst()
        
        self.create_widgets()
        self.setup_layout()
        
        # Welcome message
        self.log_message("🤖 Professional Trading Bot initialized")
        self.log_message("Ask me about any stock for comprehensive analysis")
        self.log_message("Example: 'Analyze AAPL' or 'What's your take on Tesla?'")
    
    def create_widgets(self):
        """Create professional interface"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_label = tk.Label(
            title_frame,
            text="📊 Professional Trading Bot",
            font=("Arial", 20, "bold"),
            fg='#00ff88',
            bg='#1e1e1e'
        )
        title_label.pack(pady=10)
        
        # Main content area
        self.main_frame = tk.Frame(self.root, bg='#1e1e1e')
        
        # Chat area
        self.chat_frame = tk.Frame(self.main_frame, bg='#1e1e1e')
        
        chat_label = tk.Label(
            self.chat_frame,
            text="💬 Ask for stock analysis, market insights, or trading advice",
            font=("Arial", 12, "bold"),
            fg='#ffffff',
            bg='#1e1e1e'
        )
        
        self.chat_text = scrolledtext.ScrolledText(
            self.chat_frame,
            height=30,
            width=120,
            bg='#2d2d2d',
            fg='#ffffff',
            font=("Consolas", 10),
            insertbackground='#00ff88'
        )
        
        # Input area
        self.input_frame = tk.Frame(self.chat_frame, bg='#1e1e1e')
        
        self.input_entry = tk.Entry(
            self.input_frame,
            font=("Arial", 12),
            bg='#2d2d2d',
            fg='#ffffff',
            insertbackground='#00ff88',
            width=90
        )
        
        self.send_button = tk.Button(
            self.input_frame,
            text="📊 Analyze",
            command=self.process_query,
            font=("Arial", 12, "bold"),
            bg='#00ff88',
            fg='#000000',
            height=1
        )
        
        # Quick buttons
        self.quick_frame = tk.Frame(self.main_frame, bg='#1e1e1e')
        
        quick_buttons = [
            ("📈 Market Overview", self.show_market_overview),
            ("🔥 Popular Stocks", self.show_popular_stocks),
            ("📊 Technical Scan", self.technical_scan)
        ]
        
        self.quick_buttons = []
        for text, command in quick_buttons:
            btn = tk.Button(
                self.quick_frame,
                text=text,
                command=command,
                font=("Arial", 10, "bold"),
                bg='#404040',
                fg='#ffffff',
                width=15
            )
            self.quick_buttons.append(btn)
        
        # Bind Enter key
        self.input_entry.bind('<Return>', lambda e: self.process_query())
        
        # Store frames for layout
        self.title_frame = title_frame
    
    def setup_layout(self):
        """Setup professional layout"""
        self.title_frame.pack(fill="x")
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Chat area
        self.chat_frame.pack(fill="both", expand=True)
        chat_label = self.chat_frame.winfo_children()[0]
        chat_label.pack(pady=(0, 10))
        self.chat_text.pack(fill="both", expand=True, pady=(0, 10))
        
        # Input area
        self.input_frame.pack(fill="x", pady=(0, 10))
        self.input_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.send_button.pack(side="right")
        
        # Quick buttons
        self.quick_frame.pack(fill="x")
        for btn in self.quick_buttons:
            btn.pack(side="left", padx=5)
    
    def log_message(self, message):
        """Add message to chat"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n\n"
        
        self.chat_text.insert(tk.END, formatted_message)
        self.chat_text.see(tk.END)
    
    def process_query(self):
        """Process user query"""
        query = self.input_entry.get().strip()
        if not query:
            return
        
        self.input_entry.delete(0, tk.END)
        self.log_message(f"👤 YOU: {query}")
        
        # Process in background thread
        def analyze():
            try:
                response = self.generate_response(query)
                self.log_message(f"🤖 ANALYST: {response}")
            except Exception as e:
                self.log_message(f"❌ Error: {str(e)}")
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def generate_response(self, query):
        """Generate professional response"""
        query_lower = query.lower()
        
        # Extract stock symbol
        symbol = self.extract_symbol(query)
        
        if symbol:
            # Get comprehensive analysis
            analysis = self.analyst.get_comprehensive_analysis(symbol)
            sector_analysis = self.analyst.get_sector_analysis(symbol)
            return f"{analysis}\n\n{sector_analysis}"
        
        elif any(word in query_lower for word in ['market', 'overview', 'indices']):
            return self.analyst.get_market_overview()
        
        else:
            return """
I can provide comprehensive stock analysis including:

📊 **Technical Analysis**: Moving averages, RSI, volume analysis
💰 **Price Action**: Current price, changes, 52-week range  
🎯 **Trading Signals**: Buy/sell recommendations based on indicators
⚠️ **Risk Assessment**: Risk factors and position sizing
🏭 **Sector Analysis**: Sector performance and trends

**Examples:**
• "Analyze AAPL" - Full Apple stock analysis
• "What's your take on Tesla?" - Tesla analysis  
• "Market overview" - Current market conditions
• "NVDA technical analysis" - Nvidia technical indicators

Just ask about any stock symbol!
            """.strip()
    
    def extract_symbol(self, query):
        """Extract stock symbol from query"""
        import re
        
        # Common stock mappings
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'amazon': 'AMZN', 'nvidia': 'NVDA',
            'meta': 'META', 'facebook': 'META', 'netflix': 'NFLX'
        }
        
        query_lower = query.lower()
        
        # Check for company names
        for name, symbol in stock_names.items():
            if name in query_lower:
                return symbol
        
        # Look for uppercase symbols
        symbols = re.findall(r'\b[A-Z]{1,5}\b', query)
        if symbols:
            return symbols[0]
        
        return None
    
    def show_market_overview(self):
        """Show market overview"""
        self.log_message("📈 Getting market overview...")
        
        def get_overview():
            overview = self.analyst.get_market_overview()
            self.log_message(f"🤖 MARKET UPDATE:\n{overview}")
        
        threading.Thread(target=get_overview, daemon=True).start()
    
    def show_popular_stocks(self):
        """Show analysis of popular stocks"""
        popular = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        self.log_message("🔥 Analyzing popular stocks...")
        
        def analyze_popular():
            for symbol in popular:
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    current_price = info.get('currentPrice', 0)
                    change_percent = info.get('regularMarketChangePercent', 0)
                    
                    self.log_message(f"• {symbol}: ${current_price:.2f} ({change_percent:+.2f}%)")
                except:
                    continue
        
        threading.Thread(target=analyze_popular, daemon=True).start()
    
    def technical_scan(self):
        """Run technical scan"""
        self.log_message("📊 Running technical scan on major stocks...")
        
        def scan():
            symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA']
            signals = []
            
            for symbol in symbols:
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="1mo")
                    
                    if not hist.empty:
                        current = hist['Close'].iloc[-1]
                        sma20 = hist['Close'].rolling(20).mean().iloc[-1]
                        
                        if current > sma20 * 1.02:
                            signals.append(f"🟢 {symbol}: Strong uptrend")
                        elif current < sma20 * 0.98:
                            signals.append(f"🔴 {symbol}: Downtrend")
                except:
                    continue
            
            if signals:
                self.log_message("🎯 TECHNICAL SIGNALS:\n" + "\n".join(signals))
            else:
                self.log_message("📊 No strong technical signals found")
        
        threading.Thread(target=scan, daemon=True).start()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch professional trading bot"""
    app = ProfessionalTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
