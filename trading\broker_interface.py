from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest, StopOrderRequest, TrailingStopOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce, OrderType, OrderClass
from alpaca.common.exceptions import APIError
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

class AlpacaBroker:
    def __init__(self):
        self.client = TradingClient(
            api_key=settings.ALPACA_API_KEY,
            secret_key=settings.ALPACA_SECRET_KEY,
            paper=True  # Always use paper trading for safety
        )
        self.account = None
        self.positions = {}
        self.orders = {}
        self.request_id = None
        
    async def initialize(self):
        """Initialize broker connection and get account info"""
        try:
            self.account = self.client.get_account()
            logger.info(f"Connected to Alpaca. Account status: {self.account.status}")
            logger.info(f"Buying power: ${float(self.account.buying_power):,.2f}")
            logger.info(f"Paper trading mode: {'ENABLED' if self.client._use_paper else 'DISABLED'}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca connection: {e}")
            return False

    def get_account_info(self) -> Dict:
        """Get current account information"""
        try:
            account = self.client.get_account()
            return {
                "account_id": account.id,
                "status": account.status.value,
                "equity": float(account.equity),
                "buying_power": float(account.buying_power),
                "cash": float(account.cash),
                "portfolio_value": float(account.portfolio_value),
                "day_trade_count": int(account.daytrade_count),
                "pattern_day_trader": account.pattern_day_trader
            }
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}

    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        try:
            positions = self.client.get_all_positions()
            position_list = []

            for pos in positions:
                position_list.append({
                    "symbol": pos.symbol,
                    "quantity": float(pos.qty),
                    "side": "long" if float(pos.qty) > 0 else "short",
                    "market_value": float(pos.market_value) if pos.market_value else 0.0,
                    "cost_basis": float(pos.cost_basis) if pos.cost_basis else 0.0,
                    "unrealized_pl": float(pos.unrealized_pl) if pos.unrealized_pl else 0.0,
                    "unrealized_plpc": float(pos.unrealized_plpc) if pos.unrealized_plpc else 0.0,
                    "avg_entry_price": float(pos.avg_entry_price) if pos.avg_entry_price else 0.0
                })

            return position_list
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []

    def calculate_position_size(self, symbol: str, price: float, risk_amount: float = None) -> int:
        """Calculate appropriate position size based on risk management"""
        try:
            account = self.client.get_account()
            buying_power = float(account.buying_power)

            if risk_amount is None:
                risk_amount = min(
                    buying_power * settings.RISK_PER_TRADE,
                    settings.MAX_POSITION_SIZE
                )

            # Calculate shares based on available capital
            max_shares_by_capital = int(buying_power / price)
            max_shares_by_risk = int(risk_amount / price)

            position_size = min(max_shares_by_capital, max_shares_by_risk)

            # Ensure minimum position size
            if position_size < 1:
                return 0

            logger.info(f"Calculated position size for {symbol}: {position_size} shares at ${price}")
            return position_size

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    async def place_market_order(self, symbol: str, side: str, quantity: int,
                               time_in_force: str = "day") -> Optional[Dict]:
        """Place a market order"""
        try:
            if quantity <= 0:
                logger.warning(f"Invalid quantity {quantity} for {symbol}")
                return None

            # Convert side to enum
            order_side = OrderSide.BUY if side.upper() == "BUY" else OrderSide.SELL

            # Convert time_in_force to enum
            tif_map = {
                "day": TimeInForce.DAY,
                "gtc": TimeInForce.GTC,
                "ioc": TimeInForce.IOC,
                "fok": TimeInForce.FOK
            }
            tif = tif_map.get(time_in_force.lower(), TimeInForce.DAY)

            # Create market order request
            market_order_data = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=tif
            )

            # Submit order
            order = self.client.submit_order(order_data=market_order_data)

            order_info = {
                "id": str(order.id),
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "type": "market",
                "status": order.status.value,
                "submitted_at": order.submitted_at,
                "filled_at": order.filled_at,
                "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else 0.0
            }

            logger.info(f"Market order placed: {side} {quantity} {symbol} - Order ID: {order.id}")
            return order_info

        except Exception as e:
            logger.error(f"Error placing market order for {symbol}: {e}")
            return None

    async def place_limit_order(self, symbol: str, side: str, quantity: int,
                              limit_price: float, time_in_force: str = "day") -> Optional[Dict]:
        """Place a limit order"""
        try:
            if quantity <= 0:
                logger.warning(f"Invalid quantity {quantity} for {symbol}")
                return None

            # Convert side to enum
            order_side = OrderSide.BUY if side.upper() == "BUY" else OrderSide.SELL

            # Convert time_in_force to enum
            tif_map = {
                "day": TimeInForce.DAY,
                "gtc": TimeInForce.GTC,
                "ioc": TimeInForce.IOC,
                "fok": TimeInForce.FOK
            }
            tif = tif_map.get(time_in_force.lower(), TimeInForce.DAY)

            # Create limit order request
            limit_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=tif,
                limit_price=limit_price
            )

            # Submit order
            order = self.client.submit_order(order_data=limit_order_data)

            order_info = {
                "id": str(order.id),
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "type": "limit",
                "limit_price": limit_price,
                "status": order.status.value,
                "submitted_at": order.submitted_at
            }

            logger.info(f"Limit order placed: {side} {quantity} {symbol} @ ${limit_price} - Order ID: {order.id}")
            return order_info

        except Exception as e:
            logger.error(f"Error placing limit order for {symbol}: {e}")
            return None

    async def place_stop_loss_order(self, symbol: str, side: str, quantity: int, 
                                   stop_price: float) -> Optional[Dict]:
        """Place a stop loss order"""
        try:
            order = self.api.submit_order(
                symbol=symbol,
                qty=quantity,
                side=side.lower(),
                type='stop',
                time_in_force='day',
                stop_price=stop_price
            )
            
            order_info = {
                "id": order.id,
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "type": "stop",
                "stop_price": stop_price,
                "status": order.status,
                "submitted_at": order.submitted_at
            }
            
            logger.info(f"Stop loss order placed: {side} {quantity} {symbol} @ ${stop_price}")
            return order_info
            
        except Exception as e:
            logger.error(f"Error placing stop loss order for {symbol}: {e}")
            return None

    def get_order_status(self, order_id: str) -> Optional[Dict]:
        """Get order status"""
        try:
            order = self.api.get_order(order_id)
            return {
                "id": order.id,
                "status": order.status,
                "filled_qty": int(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else 0.0,
                "filled_at": order.filled_at
            }
        except Exception as e:
            logger.error(f"Error getting order status for {order_id}: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.api.cancel_order(order_id)
            logger.info(f"Order {order_id} cancelled")
            return True
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False

    def get_market_hours(self) -> Dict:
        """Get market hours"""
        try:
            calendar = self.api.get_calendar(start=datetime.now().date(), end=datetime.now().date())
            if calendar:
                today = calendar[0]
                return {
                    "date": str(today.date),
                    "open": str(today.open),
                    "close": str(today.close),
                    "is_open": datetime.now().time() >= today.open.time() and datetime.now().time() <= today.close.time()
                }
            return {}
        except Exception as e:
            logger.error(f"Error getting market hours: {e}")
            return {}

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            clock = self.api.get_clock()
            return clock.is_open
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False
