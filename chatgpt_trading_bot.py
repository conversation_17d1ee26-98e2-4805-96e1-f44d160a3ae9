#!/usr/bin/env python3
"""
ChatGPT-Style Trading Bot
Real OpenAI API integration for intelligent trading conversations
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog
import threading
import time
from datetime import datetime
import yfinance as yf
import requests
import json
import os

# OpenAI API Configuration
OPENAI_API_KEY = "your-openai-api-key-here"  # Replace with your actual API key
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

class RealTimeMarketData:
    """Get real market data for AI context"""
    
    def get_stock_data(self, symbol):
        """Get current stock data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="5d")
            
            if hist.empty:
                return None
            
            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100
            
            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'current_price': current_price,
                'change': change,
                'change_percent': change_percent,
                'volume': hist['Volume'].iloc[-1],
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector'),
                '52w_high': info.get('fiftyTwoWeekHigh'),
                '52w_low': info.get('fiftyTwoWeekLow')
            }
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
            return None
    
    def get_market_overview(self):
        """Get market indices overview"""
        indices = {
            '^GSPC': 'S&P 500',
            '^IXIC': 'NASDAQ',
            '^DJI': 'Dow Jones'
        }
        
        market_data = {}
        for symbol, name in indices.items():
            data = self.get_stock_data(symbol)
            if data:
                market_data[name] = data
        
        return market_data

class ChatGPTTradingBot:
    """ChatGPT-style trading bot with OpenAI API"""
    
    def __init__(self):
        self.market_data = RealTimeMarketData()
        self.conversation_history = []
        
        # System prompt for trading expertise
        self.system_prompt = """You are a professional trading and investment advisor with deep expertise in:

- Technical analysis (moving averages, RSI, MACD, support/resistance)
- Fundamental analysis (P/E ratios, earnings, financial statements)
- Market trends and sector analysis
- Risk management and position sizing
- Options trading strategies
- Cryptocurrency analysis
- Economic indicators and their market impact

You provide clear, actionable trading advice while always emphasizing risk management. You can analyze real-time market data that will be provided to you. Always include specific price levels, technical indicators, and risk assessments in your responses.

You are knowledgeable, professional, and direct. You don't just give generic advice - you provide specific, actionable insights based on current market conditions."""

    def get_ai_response(self, user_message, market_context=None):
        """Get response from OpenAI API"""
        try:
            # Prepare the message with market context
            if market_context:
                enhanced_message = f"""
User Question: {user_message}

Current Market Data:
{market_context}

Please provide a comprehensive analysis considering this real-time data.
"""
            else:
                enhanced_message = user_message
            
            # Prepare conversation for API
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (last 10 messages)
            for msg in self.conversation_history[-10:]:
                messages.append(msg)
            
            # Add current message
            messages.append({"role": "user", "content": enhanced_message})
            
            # Make API request
            headers = {
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": messages,
                "max_tokens": 1500,
                "temperature": 0.7
            }
            
            response = requests.post(OPENAI_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Add to conversation history
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                return ai_response
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error getting AI response: {str(e)}"
    
    def extract_symbols_from_message(self, message):
        """Extract stock symbols from user message"""
        import re
        
        # Common stock mappings
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'alphabet': 'GOOGL', 'amazon': 'AMZN', 
            'nvidia': 'NVDA', 'meta': 'META', 'facebook': 'META',
            'netflix': 'NFLX', 'amd': 'AMD', 'intel': 'INTC'
        }
        
        message_lower = message.lower()
        symbols = []
        
        # Check for company names
        for name, symbol in stock_names.items():
            if name in message_lower:
                symbols.append(symbol)
        
        # Look for uppercase symbols
        found_symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
        symbols.extend(found_symbols)
        
        return list(set(symbols))  # Remove duplicates
    
    def get_market_context(self, symbols):
        """Get market context for the symbols mentioned"""
        if not symbols:
            return None
        
        context = "Real-time Market Data:\n\n"
        
        for symbol in symbols[:3]:  # Limit to 3 symbols to avoid too much data
            data = self.market_data.get_stock_data(symbol)
            if data:
                context += f"""
{symbol} ({data['name']}):
- Current Price: ${data['current_price']:.2f}
- Change: {data['change']:+.2f} ({data['change_percent']:+.2f}%)
- Volume: {data['volume']:,}
- Market Cap: ${data['market_cap']/1e9:.1f}B (if available)
- P/E Ratio: {data['pe_ratio']:.2f if data['pe_ratio'] else 'N/A'}
- 52W Range: ${data['52w_low']:.2f} - ${data['52w_high']:.2f}
- Sector: {data['sector']}

"""
        
        return context if len(context) > 50 else None

class ChatGPTTradingGUI:
    """ChatGPT-style interface for trading bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ChatGPT Trading Bot - Real AI Conversations")
        self.root.geometry("1000x700")
        self.root.configure(bg='#343541')
        
        self.bot = ChatGPTTradingBot()
        self.create_widgets()
        self.setup_layout()
        
        # Welcome message
        self.add_message("🤖 AI Trading Assistant", 
                        "Hello! I'm your AI trading assistant powered by GPT-4. I can help you with:\n\n" +
                        "📊 Stock analysis and recommendations\n" +
                        "📈 Technical analysis and chart patterns\n" +
                        "💰 Investment strategies and risk management\n" +
                        "🔍 Market research and sector analysis\n" +
                        "📉 Options trading strategies\n\n" +
                        "Just ask me anything about trading or specific stocks!")
    
    def create_widgets(self):
        """Create ChatGPT-style interface"""
        
        # Main chat area
        self.chat_frame = tk.Frame(self.root, bg='#343541')
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            bg='#444654',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            state=tk.DISABLED,
            insertbackground='#ffffff'
        )
        
        # Input area
        self.input_frame = tk.Frame(self.root, bg='#40414f')
        
        # Input text area
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            bg='#40414f',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            insertbackground='#ffffff',
            relief=tk.FLAT,
            bd=10
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="Send",
            command=self.send_message,
            bg='#10a37f',
            fg='#ffffff',
            font=('Segoe UI', 10, 'bold'),
            relief=tk.FLAT,
            padx=20,
            cursor='hand2'
        )
        
        # Bind Enter key (Shift+Enter for new line)
        self.input_text.bind('<Return>', self.on_enter)
        self.input_text.bind('<Shift-Return>', self.on_shift_enter)
        
        # API key setup button
        self.setup_button = tk.Button(
            self.input_frame,
            text="Setup API Key",
            command=self.setup_api_key,
            bg='#ff6b6b',
            fg='#ffffff',
            font=('Segoe UI', 9),
            relief=tk.FLAT
        )
    
    def setup_layout(self):
        """Setup ChatGPT-style layout"""
        # Chat area
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 0))
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        
        # Input area
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Input layout
        self.setup_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.send_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def add_message(self, sender, message):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M")
        
        # Add sender and message
        self.chat_display.insert(tk.END, f"\n{sender} [{timestamp}]\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags for styling
        self.chat_display.tag_config("sender", foreground="#10a37f", font=('Segoe UI', 11, 'bold'))
        self.chat_display.tag_config("message", foreground="#ffffff", font=('Segoe UI', 11))
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def send_message(self):
        """Send message to AI"""
        message = self.input_text.get("1.0", tk.END).strip()
        if not message:
            return
        
        # Clear input
        self.input_text.delete("1.0", tk.END)
        
        # Add user message
        self.add_message("👤 You", message)
        
        # Show thinking indicator
        self.add_message("🤖 AI Assistant", "🤔 Thinking...")
        
        # Process in background
        def get_response():
            try:
                # Extract symbols and get market context
                symbols = self.bot.extract_symbols_from_message(message)
                market_context = self.bot.get_market_context(symbols)
                
                # Get AI response
                response = self.bot.get_ai_response(message, market_context)
                
                # Remove thinking message and add real response
                self.chat_display.config(state=tk.NORMAL)
                
                # Find and remove the last "Thinking..." message
                content = self.chat_display.get("1.0", tk.END)
                lines = content.split('\n')
                
                # Remove last few lines (thinking message)
                for i in range(4):
                    if lines:
                        lines.pop()
                
                # Rebuild content without thinking message
                new_content = '\n'.join(lines)
                self.chat_display.delete("1.0", tk.END)
                self.chat_display.insert("1.0", new_content)
                
                self.chat_display.config(state=tk.DISABLED)
                
                # Add real response
                self.add_message("🤖 AI Assistant", response)
                
            except Exception as e:
                self.add_message("❌ Error", f"Failed to get response: {str(e)}")
        
        threading.Thread(target=get_response, daemon=True).start()
    
    def on_enter(self, event):
        """Handle Enter key"""
        self.send_message()
        return "break"
    
    def on_shift_enter(self, event):
        """Handle Shift+Enter for new line"""
        return None
    
    def setup_api_key(self):
        """Setup OpenAI API key"""
        api_key = simpledialog.askstring(
            "OpenAI API Key",
            "Enter your OpenAI API key:",
            show='*'
        )
        
        if api_key:
            global OPENAI_API_KEY
            OPENAI_API_KEY = api_key
            self.add_message("✅ System", "API key configured! You can now chat with the AI.")
        else:
            self.add_message("⚠️ System", "API key required for AI responses.")
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch ChatGPT-style trading bot"""
    
    print("🚀 Launching ChatGPT Trading Bot...")
    print("📊 Real AI conversations about trading and markets")
    
    app = ChatGPTTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
