# 🤖 Autonomous Trading Bot - Desktop Application

A sophisticated, AI-powered desktop trading application that continuously scans markets, detects patterns, executes trades, and provides real-time notifications. Built with Python, modern GUI (CustomTkinter), OpenAI GPT-4, and integrated with Alpaca for trading execution.

## 🚀 Features

### Core Functionality
- **Modern Desktop GUI** - Professional interface with dark theme and real-time updates
- **Real-time Market Scanning** - Continuously monitors markets for new opportunities
- **Pattern Recognition** - Detects breakouts, RSI signals, volume spikes, MACD crossovers, and more
- **AI-Powered Analysis** - Uses OpenAI GPT-4 for intelligent market analysis and trade decisions
- **Automated Trading** - Executes trades via Alpaca API with proper risk management
- **Proactive Notifications** - Voice alerts, desktop notifications, and interruptions for urgent signals

### Advanced Features
- **Professional Desktop Interface** - Modern GUI with tabbed interface and real-time charts
- **Multi-source Data** - Yahoo Finance, Financial Modeling Prep, Alpaca data feeds
- **Technical Indicators** - RSI, MACD, Bollinger Bands, Moving Averages, Stochastic
- **Risk Management** - Position sizing, stop losses, daily trade limits
- **Live Monitoring** - Real-time pattern detection, trade execution, and account monitoring
- **Database Logging** - Complete trade and pattern history
- **Paper Trading** - Safe testing environment

## 📋 Prerequisites

- Python 3.8+
- API Keys:
  - OpenAI API Key
  - Alpaca Trading API Key & Secret
  - Financial Modeling Prep API Key

## 🛠️ Installation

1. **Clone or download the project**
   ```bash
   cd Stockbot
   ```

2. **Install dependencies**
   ```bash
   python run_bot.py install
   ```

3. **Setup environment**
   ```bash
   python run_bot.py setup
   ```

4. **Test connections**
   ```bash
   python run_bot.py test
   ```

## ⚙️ Configuration

Your API keys are already configured in `config/settings.py`:

- **OpenAI API Key**: `********************************************************************************************************************************************************************`
- **Alpaca API Key**: `PK3CUPBGD0EK2S6GND10`
- **Alpaca Secret**: `xsybRgWQGNHpOZFJeeVQIb45tbwwEFUM53xQXyaO`
- **FMP API Key**: `K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7`

### Trading Settings
- **Max Position Size**: $1,000 per trade
- **Risk Per Trade**: 2% of account
- **Max Daily Trades**: 10
- **Scan Interval**: 30 seconds
- **Paper Trading**: Enabled (safe mode)

## 🚀 Usage

### Quick Start - Desktop App
```bash
# Run desktop application (RECOMMENDED)
python run_bot.py desktop

# Or double-click start_desktop.bat
```

### Alternative Options
```bash
# Run console bot only
python run_bot.py bot

# Run web dashboard only
python run_bot.py dashboard

# Check system status
python run_bot.py status
```

### Desktop Application Features
The main desktop interface includes:

**📊 Dashboard Tab**
- Real-time account balance and P&L
- Live pattern detection alerts
- Market status and bot controls

**💰 Trades Tab**
- Complete trade execution history
- Real-time trade status updates
- Profit/loss tracking

**📈 Positions Tab**
- Current open positions
- Unrealized P&L monitoring
- Position sizing information

**📝 Logs Tab**
- Real-time system logs
- Error tracking and debugging
- Activity monitoring

**⚙️ Settings Tab**
- Trading parameters configuration
- Notification preferences
- Risk management settings

## 🎯 Pattern Detection

The bot detects multiple trading patterns:

### Technical Patterns
- **Breakout Patterns** - Price breaks above resistance or below support
- **RSI Signals** - Oversold/overbought conditions with reversal potential
- **Volume Spikes** - Unusual volume indicating institutional activity
- **MACD Crossovers** - Bullish/bearish momentum shifts
- **Golden/Death Cross** - 50/200 SMA crossovers

### AI Analysis
Each detected pattern is analyzed by GPT-4 for:
- Risk assessment
- Success probability
- Position sizing recommendations
- Price targets
- Overall trade recommendation

## 🔔 Notification System

### Voice Alerts
- Text-to-speech notifications for important patterns
- Configurable voice rate and volume
- Priority-based alert system

### Desktop Notifications
- Pop-up notifications for all detected patterns
- Color-coded by action type (buy/sell/watch)
- Persistent alerts for high-priority signals

### Interruption System
- Emergency alerts for critical market events
- Multiple notification attempts for urgent signals
- Designed to get your attention immediately

## 📊 Risk Management

### Position Sizing
- Automatic calculation based on account size and risk tolerance
- Maximum position size limits
- Risk per trade percentage controls

### Trade Limits
- Daily trade count limits
- Maximum exposure controls
- Stop-loss integration

### Paper Trading
- All trades executed in paper trading mode by default
- Safe testing environment
- Real market data with simulated execution

## 🗄️ Database Schema

The bot maintains comprehensive records:
- **Stocks** - Symbol information and market data
- **Trades** - Complete trade history with P&L
- **Patterns** - All detected patterns with confidence scores
- **Alerts** - Notification history and delivery status
- **Market Data** - Historical price and indicator data

## 📈 Performance Monitoring

### Metrics Tracked
- Win rate and profit factor
- Average win/loss amounts
- Maximum drawdown
- Sharpe ratio
- Daily/monthly returns

### Reporting
- Real-time dashboard metrics
- Historical performance analysis
- Trade-by-trade breakdown
- Pattern success rates

## 🔧 Customization

### Adding New Patterns
1. Create pattern detection method in `analysis/pattern_detector.py`
2. Add pattern to analysis pipeline
3. Configure confidence thresholds

### Modifying Risk Parameters
Edit `config/settings.py`:
```python
MAX_POSITION_SIZE = 2000.0  # Increase position size
RISK_PER_TRADE = 0.01       # Reduce risk to 1%
MAX_DAILY_TRADES = 20       # Allow more trades
```

### Custom Watchlists
Add symbols to monitor:
```python
WATCHLIST_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA",
    "YOUR_CUSTOM_SYMBOLS_HERE"
]
```

## 🚨 Safety Features

- **Paper Trading Default** - No real money at risk initially
- **Position Size Limits** - Prevents over-exposure
- **Daily Trade Limits** - Prevents over-trading
- **Error Handling** - Comprehensive exception management
- **Logging** - Complete audit trail of all activities

## 📝 Logging

All activities are logged to:
- `trading_bot.log` - Main application log
- Console output - Real-time status updates
- Database records - Persistent trade and pattern history

## 🤝 Support

For issues or questions:
1. Check the logs in `trading_bot.log`
2. Run `python run_bot.py test` to verify connections
3. Use `python run_bot.py status` to check configuration

## ⚠️ Disclaimer

This software is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Always test thoroughly in paper trading mode before using real money.

## 📄 License

This project is provided as-is for educational purposes. Use at your own risk.

---

**Happy Trading! 🚀📈**
