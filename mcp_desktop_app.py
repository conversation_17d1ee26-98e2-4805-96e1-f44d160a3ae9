#!/usr/bin/env python3
"""
Enhanced Desktop Trading Application with MCP Integration
Professional-grade trading interface using Alpaca MCP server
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import customtkinter as ctk
import threading
import asyncio
import queue
import time
from datetime import datetime
import logging
import json

# Import MCP integration
from mcp_integration import mcp_engine
from alpaca_mcp_server import (
    get_account_info, get_positions, get_stock_quote, 
    get_orders, get_market_clock
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MCPTradingGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🚀 Professional Trading Bot - MCP Powered")
        self.root.geometry("1600x1000")
        self.root.minsize(1400, 900)
        
        # State variables
        self.message_queue = queue.Queue()
        self.is_running = False
        self.watchlist = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN", "NVDA", "META"]
        
        # Create GUI
        self.create_widgets()
        self.setup_layout()
        
        # Start message processor
        self.process_messages()
        
        # Initialize MCP engine
        self.initialize_mcp()

    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🚀 Professional Trading Bot - MCP Powered", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        
        # Status frame with account info
        self.status_frame = ctk.CTkFrame(self.main_frame)
        
        # Account dashboard
        self.account_frame = ctk.CTkFrame(self.main_frame)
        
        # Account info cards
        self.create_account_cards()
        
        # Control panel
        self.control_frame = ctk.CTkFrame(self.main_frame)
        self.create_control_buttons()
        
        # Strategy selection
        self.strategy_frame = ctk.CTkFrame(self.main_frame)
        self.create_strategy_controls()
        
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.create_tabs()

    def create_account_cards(self):
        """Create account information cards"""
        
        # Buying Power
        self.buying_power_label = ctk.CTkLabel(self.account_frame, text="Buying Power", font=ctk.CTkFont(size=12))
        self.buying_power_value = ctk.CTkLabel(self.account_frame, text="$0.00", font=ctk.CTkFont(size=18, weight="bold"))
        
        # Portfolio Value
        self.portfolio_label = ctk.CTkLabel(self.account_frame, text="Portfolio Value", font=ctk.CTkFont(size=12))
        self.portfolio_value = ctk.CTkLabel(self.account_frame, text="$0.00", font=ctk.CTkFont(size=18, weight="bold"))
        
        # Daily P&L
        self.pnl_label = ctk.CTkLabel(self.account_frame, text="Daily P&L", font=ctk.CTkFont(size=12))
        self.pnl_value = ctk.CTkLabel(self.account_frame, text="$0.00", font=ctk.CTkFont(size=18, weight="bold"))
        
        # Active Positions
        self.positions_label = ctk.CTkLabel(self.account_frame, text="Active Positions", font=ctk.CTkFont(size=12))
        self.positions_count = ctk.CTkLabel(self.account_frame, text="0", font=ctk.CTkFont(size=18, weight="bold"))
        
        # Market Status
        self.market_label = ctk.CTkLabel(self.account_frame, text="Market Status", font=ctk.CTkFont(size=12))
        self.market_status = ctk.CTkLabel(self.account_frame, text="Closed", font=ctk.CTkFont(size=18, weight="bold"))

    def create_control_buttons(self):
        """Create control buttons"""
        
        self.start_button = ctk.CTkButton(
            self.control_frame, 
            text="🚀 Start MCP Engine", 
            command=self.start_mcp_engine,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            width=150
        )
        
        self.stop_button = ctk.CTkButton(
            self.control_frame, 
            text="⏹️ Stop Engine", 
            command=self.stop_mcp_engine,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            width=150,
            state="disabled"
        )
        
        self.refresh_button = ctk.CTkButton(
            self.control_frame, 
            text="🔄 Refresh Data", 
            command=self.refresh_account_data,
            font=ctk.CTkFont(size=14),
            height=40,
            width=150
        )
        
        self.emergency_button = ctk.CTkButton(
            self.control_frame, 
            text="🚨 Emergency Stop", 
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            width=150,
            fg_color="red"
        )
        
        # Status indicator
        self.status_label = ctk.CTkLabel(
            self.control_frame, 
            text="Status: Stopped", 
            font=ctk.CTkFont(size=14, weight="bold")
        )

    def create_strategy_controls(self):
        """Create strategy selection controls"""
        
        strategy_title = ctk.CTkLabel(self.strategy_frame, text="Active Strategies", font=ctk.CTkFont(size=16, weight="bold"))
        
        # Strategy checkboxes
        self.ttm_squeeze_var = tk.BooleanVar(value=True)
        self.ttm_checkbox = ctk.CTkCheckBox(self.strategy_frame, text="TTM Squeeze", variable=self.ttm_squeeze_var)
        
        self.stddev_atr_var = tk.BooleanVar(value=True)
        self.stddev_checkbox = ctk.CTkCheckBox(self.strategy_frame, text="StdDev/ATR", variable=self.stddev_atr_var)
        
        self.momentum_var = tk.BooleanVar(value=False)
        self.momentum_checkbox = ctk.CTkCheckBox(self.strategy_frame, text="Momentum", variable=self.momentum_var)
        
        # Watchlist
        watchlist_title = ctk.CTkLabel(self.strategy_frame, text="Watchlist", font=ctk.CTkFont(size=14, weight="bold"))
        self.watchlist_text = ctk.CTkTextbox(self.strategy_frame, height=60, width=200)
        self.watchlist_text.insert("0.0", ", ".join(self.watchlist))

    def create_tabs(self):
        """Create notebook tabs"""
        
        # Live Signals Tab
        self.signals_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.signals_frame, text="🎯 Live Signals")
        
        self.signals_tree = ttk.Treeview(
            self.signals_frame,
            columns=("Time", "Symbol", "Strategy", "Signal", "Confidence", "Price", "Action"),
            show="headings",
            height=12
        )
        
        for col in ("Time", "Symbol", "Strategy", "Signal", "Confidence", "Price", "Action"):
            self.signals_tree.heading(col, text=col)
            self.signals_tree.column(col, width=120)
        
        # Trades Tab
        self.trades_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.trades_frame, text="💰 Trades")
        
        self.trades_tree = ttk.Treeview(
            self.trades_frame,
            columns=("Time", "Symbol", "Side", "Quantity", "Price", "Status", "P&L"),
            show="headings",
            height=12
        )
        
        for col in ("Time", "Symbol", "Side", "Quantity", "Price", "Status", "P&L"):
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=100)
        
        # Positions Tab
        self.positions_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.positions_frame, text="📈 Positions")
        
        self.positions_tree = ttk.Treeview(
            self.positions_frame,
            columns=("Symbol", "Quantity", "Avg Price", "Current Price", "Market Value", "P&L", "P&L %"),
            show="headings",
            height=12
        )
        
        for col in ("Symbol", "Quantity", "Avg Price", "Current Price", "Market Value", "P&L", "P&L %"):
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=120)
        
        # MCP Console Tab
        self.console_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.console_frame, text="🖥️ MCP Console")
        
        self.console_text = scrolledtext.ScrolledText(
            self.console_frame,
            height=20,
            bg="#2b2b2b",
            fg="#00ff00",
            font=("Consolas", 10)
        )

    def setup_layout(self):
        """Setup widget layout"""
        
        # Title
        self.title_label.pack(pady=(0, 20))
        
        # Account info
        self.account_frame.pack(fill="x", pady=(0, 10))
        
        # Account cards grid
        self.buying_power_label.grid(row=0, column=0, padx=20, pady=5)
        self.buying_power_value.grid(row=1, column=0, padx=20, pady=5)
        
        self.portfolio_label.grid(row=0, column=1, padx=20, pady=5)
        self.portfolio_value.grid(row=1, column=1, padx=20, pady=5)
        
        self.pnl_label.grid(row=0, column=2, padx=20, pady=5)
        self.pnl_value.grid(row=1, column=2, padx=20, pady=5)
        
        self.positions_label.grid(row=0, column=3, padx=20, pady=5)
        self.positions_count.grid(row=1, column=3, padx=20, pady=5)
        
        self.market_label.grid(row=0, column=4, padx=20, pady=5)
        self.market_status.grid(row=1, column=4, padx=20, pady=5)
        
        # Control buttons
        self.control_frame.pack(fill="x", pady=(0, 10))
        
        self.start_button.pack(side="left", padx=10)
        self.stop_button.pack(side="left", padx=10)
        self.refresh_button.pack(side="left", padx=10)
        self.emergency_button.pack(side="left", padx=10)
        self.status_label.pack(side="right", padx=10)
        
        # Strategy controls
        self.strategy_frame.pack(fill="x", pady=(0, 10))
        
        # Notebook
        self.notebook.pack(fill="both", expand=True)
        
        # Pack tree views
        self.signals_tree.pack(fill="both", expand=True, padx=10, pady=10)
        self.trades_tree.pack(fill="both", expand=True, padx=10, pady=10)
        self.positions_tree.pack(fill="both", expand=True, padx=10, pady=10)
        self.console_text.pack(fill="both", expand=True, padx=10, pady=10)

    def log_message(self, message: str, level: str = "INFO"):
        """Add message to console"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.message_queue.put(("console", log_entry))
        logger.info(message)

    def process_messages(self):
        """Process messages from queue"""
        try:
            while True:
                msg_type, data = self.message_queue.get_nowait()
                
                if msg_type == "console":
                    self.console_text.insert(tk.END, data)
                    self.console_text.see(tk.END)
                elif msg_type == "signal":
                    self.add_signal_to_tree(data)
                elif msg_type == "trade":
                    self.add_trade_to_tree(data)
                elif msg_type == "account":
                    self.update_account_display(data)
                    
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_messages)

    def add_signal_to_tree(self, signal: dict):
        """Add signal to signals tree"""
        values = (
            datetime.now().strftime("%H:%M:%S"),
            signal.get('symbol', ''),
            signal.get('strategy', ''),
            signal.get('signal', ''),
            f"{signal.get('confidence', 0)*100:.0f}%",
            f"${signal.get('entry_price', 0):.2f}",
            signal.get('action', 'WATCH')
        )
        
        self.signals_tree.insert("", 0, values=values)

    def add_trade_to_tree(self, trade: dict):
        """Add trade to trades tree"""
        values = (
            datetime.now().strftime("%H:%M:%S"),
            trade.get('symbol', ''),
            trade.get('side', ''),
            trade.get('quantity', 0),
            f"${trade.get('entry_price', 0):.2f}",
            trade.get('status', ''),
            f"${trade.get('pnl', 0):.2f}"
        )
        
        self.trades_tree.insert("", 0, values=values)

    def update_account_display(self, account_data: dict):
        """Update account information display"""
        # This would parse the MCP account data and update the display
        self.log_message("Account data updated")

    def initialize_mcp(self):
        """Initialize MCP engine"""
        def init_async():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                success = loop.run_until_complete(mcp_engine.initialize())
                if success:
                    self.log_message("✅ MCP Engine initialized successfully")
                else:
                    self.log_message("❌ Failed to initialize MCP Engine", "ERROR")
                
                loop.close()
                
            except Exception as e:
                self.log_message(f"Error initializing MCP: {e}", "ERROR")
        
        init_thread = threading.Thread(target=init_async, daemon=True)
        init_thread.start()

    def start_mcp_engine(self):
        """Start the MCP trading engine"""
        if self.is_running:
            return
        
        self.is_running = True
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.status_label.configure(text="Status: Running", text_color="green")
        
        self.log_message("🚀 Starting MCP Trading Engine...")
        
        # Get watchlist from text box
        watchlist_text = self.watchlist_text.get("0.0", tk.END).strip()
        symbols = [s.strip() for s in watchlist_text.split(",") if s.strip()]
        
        def run_async():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                loop.run_until_complete(mcp_engine.run_continuous_strategy(symbols))
                
            except Exception as e:
                self.log_message(f"Error in MCP engine: {e}", "ERROR")
        
        engine_thread = threading.Thread(target=run_async, daemon=True)
        engine_thread.start()

    def stop_mcp_engine(self):
        """Stop the MCP trading engine"""
        self.is_running = False
        mcp_engine.stop()
        
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.status_label.configure(text="Status: Stopped", text_color="red")
        
        self.log_message("⏹️ MCP Trading Engine stopped")

    def refresh_account_data(self):
        """Refresh account data using MCP"""
        def refresh_async():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Get account info
                account_info = loop.run_until_complete(get_account_info())
                self.log_message("Account Info:")
                self.log_message(account_info)
                
                # Get positions
                positions = loop.run_until_complete(get_positions())
                self.log_message("Positions:")
                self.log_message(positions)
                
                # Get market status
                market_clock = loop.run_until_complete(get_market_clock())
                self.log_message("Market Status:")
                self.log_message(market_clock)
                
                loop.close()
                
            except Exception as e:
                self.log_message(f"Error refreshing data: {e}", "ERROR")
        
        refresh_thread = threading.Thread(target=refresh_async, daemon=True)
        refresh_thread.start()

    def emergency_stop(self):
        """Emergency stop all operations"""
        if messagebox.askyesno("Emergency Stop", "This will stop all trading operations and cancel open orders. Continue?"):
            self.stop_mcp_engine()
            self.log_message("🚨 EMERGENCY STOP ACTIVATED", "WARNING")

    def run(self):
        """Run the application"""
        self.log_message("🚀 Professional Trading Bot - MCP Powered")
        self.log_message("Ready to trade with enterprise-grade infrastructure")
        self.root.mainloop()

def main():
    """Main function"""
    app = MCPTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
