#!/usr/bin/env python3
"""
Simple launcher for the Trading Bot Desktop Application
"""

import sys
import subprocess
import tkinter as tk
from tkinter import messagebox
import os

def check_python():
    """Check if Python is properly installed"""
    try:
        import sys
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            return True
        else:
            messagebox.showerror("Python Version", f"Python 3.8+ required. Found: {version.major}.{version.minor}")
            return False
    except:
        messagebox.showerror("Python Error", "Python not found or not working properly")
        return False

def install_dependencies():
    """Install required dependencies"""
    try:
        messagebox.showinfo("Installing", "Installing dependencies... This may take a few minutes.")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            messagebox.showinfo("Success", "Dependencies installed successfully!")
            return True
        else:
            messagebox.showerror("Error", f"Failed to install dependencies:\n{result.stderr}")
            return False
    except Exception as e:
        messagebox.showerror("Error", f"Installation error: {e}")
        return False

def launch_desktop_app():
    """Launch the desktop application"""
    try:
        # Check if desktop_app.py exists
        if not os.path.exists("desktop_app.py"):
            messagebox.showerror("Error", "desktop_app.py not found!")
            return False
        
        # Launch the app
        subprocess.Popen([sys.executable, "desktop_app.py"])
        return True
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to launch application: {e}")
        return False

def main():
    """Main launcher function"""
    # Create a simple launcher window
    root = tk.Tk()
    root.title("Trading Bot Launcher")
    root.geometry("400x300")
    root.resizable(False, False)
    
    # Center the window
    root.eval('tk::PlaceWindow . center')
    
    # Title
    title_label = tk.Label(root, text="🤖 Autonomous Trading Bot", 
                          font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    # Description
    desc_label = tk.Label(root, text="Desktop Application Launcher", 
                         font=("Arial", 12))
    desc_label.pack(pady=10)
    
    # Buttons frame
    buttons_frame = tk.Frame(root)
    buttons_frame.pack(pady=20)
    
    # Install button
    install_btn = tk.Button(buttons_frame, text="📦 Install Dependencies", 
                           command=install_dependencies,
                           font=("Arial", 12),
                           bg="#4CAF50", fg="white",
                           width=20, height=2)
    install_btn.pack(pady=5)
    
    # Launch button
    launch_btn = tk.Button(buttons_frame, text="🚀 Launch Trading Bot", 
                          command=lambda: launch_and_close(root),
                          font=("Arial", 12, "bold"),
                          bg="#2196F3", fg="white",
                          width=20, height=2)
    launch_btn.pack(pady=5)
    
    # Test button
    test_btn = tk.Button(buttons_frame, text="🔧 Test System", 
                        command=test_system,
                        font=("Arial", 12),
                        bg="#FF9800", fg="white",
                        width=20, height=2)
    test_btn.pack(pady=5)
    
    # Status label
    status_label = tk.Label(root, text="Ready to launch", 
                           font=("Arial", 10),
                           fg="green")
    status_label.pack(pady=10)
    
    # Check Python version on startup
    if not check_python():
        status_label.config(text="Python 3.8+ required", fg="red")
        launch_btn.config(state="disabled")
    
    root.mainloop()

def launch_and_close(root):
    """Launch app and close launcher"""
    if launch_desktop_app():
        root.destroy()

def test_system():
    """Test system connections"""
    try:
        messagebox.showinfo("Testing", "Testing system... This may take a moment.")
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            messagebox.showinfo("Test Results", "System test completed! Check console for details.")
        else:
            messagebox.showwarning("Test Results", f"Some tests failed:\n{result.stderr[:200]}...")
            
    except subprocess.TimeoutExpired:
        messagebox.showwarning("Test Results", "Test timed out. System may be slow to respond.")
    except Exception as e:
        messagebox.showerror("Test Error", f"Test failed: {e}")

if __name__ == "__main__":
    main()
