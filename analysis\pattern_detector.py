import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

class PatternDetector:
    def __init__(self):
        self.patterns = []
        
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for pattern detection"""
        try:
            # RSI
            df['RSI'] = ta.momentum.RSIIndicator(df['Close']).rsi()
            
            # Moving Averages
            df['SMA_20'] = ta.trend.SMAIndicator(df['Close'], window=20).sma_indicator()
            df['SMA_50'] = ta.trend.SMAIndicator(df['Close'], window=50).sma_indicator()
            df['EMA_12'] = ta.trend.EMAIndicator(df['Close'], window=12).ema_indicator()
            df['EMA_26'] = ta.trend.EMAIndicator(df['Close'], window=26).ema_indicator()
            
            # MACD
            macd = ta.trend.MACD(df['Close'])
            df['MACD'] = macd.macd()
            df['MACD_Signal'] = macd.macd_signal()
            df['MACD_Histogram'] = macd.macd_diff()
            
            # Bollinger Bands
            bb = ta.volatility.BollingerBands(df['Close'])
            df['BB_Upper'] = bb.bollinger_hband()
            df['BB_Lower'] = bb.bollinger_lband()
            df['BB_Middle'] = bb.bollinger_mavg()
            
            # Volume indicators
            df['Volume_SMA'] = ta.volume.VolumeSMAIndicator(df['Close'], df['Volume']).volume_sma()
            
            # Stochastic
            stoch = ta.momentum.StochasticOscillator(df['High'], df['Low'], df['Close'])
            df['Stoch_K'] = stoch.stoch()
            df['Stoch_D'] = stoch.stoch_signal()
            
            return df
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return df

    def detect_breakout_pattern(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect breakout patterns"""
        try:
            if len(df) < 20:
                return None
                
            latest = df.iloc[-1]
            prev_high = df['High'].iloc[-20:-1].max()
            
            # Price breakout above resistance
            if latest['Close'] > prev_high * (1 + settings.BREAKOUT_THRESHOLD):
                volume_spike = latest['Volume'] > df['Volume'].iloc[-20:-1].mean() * settings.VOLUME_SPIKE_MULTIPLIER
                
                return {
                    "symbol": symbol,
                    "pattern_type": "BREAKOUT_BULLISH",
                    "confidence": 0.8 if volume_spike else 0.6,
                    "price": latest['Close'],
                    "volume": latest['Volume'],
                    "description": f"Bullish breakout above {prev_high:.2f} resistance",
                    "action": "BUY" if volume_spike else "WATCH"
                }
                
            # Price breakdown below support
            prev_low = df['Low'].iloc[-20:-1].min()
            if latest['Close'] < prev_low * (1 - settings.BREAKOUT_THRESHOLD):
                volume_spike = latest['Volume'] > df['Volume'].iloc[-20:-1].mean() * settings.VOLUME_SPIKE_MULTIPLIER
                
                return {
                    "symbol": symbol,
                    "pattern_type": "BREAKOUT_BEARISH",
                    "confidence": 0.8 if volume_spike else 0.6,
                    "price": latest['Close'],
                    "volume": latest['Volume'],
                    "description": f"Bearish breakdown below {prev_low:.2f} support",
                    "action": "SELL" if volume_spike else "WATCH"
                }
                
        except Exception as e:
            logger.error(f"Error detecting breakout for {symbol}: {e}")
            
        return None

    def detect_rsi_patterns(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect RSI-based patterns"""
        try:
            if 'RSI' not in df.columns or len(df) < 5:
                return None
                
            latest_rsi = df['RSI'].iloc[-1]
            prev_rsi = df['RSI'].iloc[-2]
            
            # RSI Oversold reversal
            if latest_rsi < settings.RSI_OVERSOLD and prev_rsi > latest_rsi:
                return {
                    "symbol": symbol,
                    "pattern_type": "RSI_OVERSOLD",
                    "confidence": 0.7,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": f"RSI oversold at {latest_rsi:.1f}, potential reversal",
                    "action": "BUY"
                }
                
            # RSI Overbought reversal
            if latest_rsi > settings.RSI_OVERBOUGHT and prev_rsi < latest_rsi:
                return {
                    "symbol": symbol,
                    "pattern_type": "RSI_OVERBOUGHT",
                    "confidence": 0.7,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": f"RSI overbought at {latest_rsi:.1f}, potential reversal",
                    "action": "SELL"
                }
                
        except Exception as e:
            logger.error(f"Error detecting RSI patterns for {symbol}: {e}")
            
        return None

    def detect_volume_spike(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect unusual volume spikes"""
        try:
            if len(df) < 20:
                return None
                
            latest_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume'].iloc[-20:-1].mean()
            
            if latest_volume > avg_volume * settings.VOLUME_SPIKE_MULTIPLIER:
                price_change = (df['Close'].iloc[-1] - df['Close'].iloc[-2]) / df['Close'].iloc[-2]
                
                return {
                    "symbol": symbol,
                    "pattern_type": "VOLUME_SPIKE",
                    "confidence": 0.6,
                    "price": df['Close'].iloc[-1],
                    "volume": latest_volume,
                    "description": f"Volume spike: {latest_volume/avg_volume:.1f}x average, price change: {price_change*100:.1f}%",
                    "action": "BUY" if price_change > 0 else "SELL"
                }
                
        except Exception as e:
            logger.error(f"Error detecting volume spike for {symbol}: {e}")
            
        return None

    def detect_macd_crossover(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect MACD crossover patterns"""
        try:
            if 'MACD' not in df.columns or len(df) < 5:
                return None
                
            macd_current = df['MACD'].iloc[-1]
            macd_prev = df['MACD'].iloc[-2]
            signal_current = df['MACD_Signal'].iloc[-1]
            signal_prev = df['MACD_Signal'].iloc[-2]
            
            # Bullish crossover
            if macd_prev <= signal_prev and macd_current > signal_current:
                return {
                    "symbol": symbol,
                    "pattern_type": "MACD_BULLISH_CROSSOVER",
                    "confidence": 0.65,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": "MACD bullish crossover detected",
                    "action": "BUY"
                }
                
            # Bearish crossover
            if macd_prev >= signal_prev and macd_current < signal_current:
                return {
                    "symbol": symbol,
                    "pattern_type": "MACD_BEARISH_CROSSOVER",
                    "confidence": 0.65,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": "MACD bearish crossover detected",
                    "action": "SELL"
                }
                
        except Exception as e:
            logger.error(f"Error detecting MACD crossover for {symbol}: {e}")
            
        return None

    def detect_golden_cross(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect Golden Cross (50 SMA crosses above 200 SMA)"""
        try:
            if 'SMA_50' not in df.columns or len(df) < 200:
                return None
                
            # Calculate 200 SMA
            df['SMA_200'] = ta.trend.SMAIndicator(df['Close'], window=200).sma_indicator()
            
            sma50_current = df['SMA_50'].iloc[-1]
            sma50_prev = df['SMA_50'].iloc[-2]
            sma200_current = df['SMA_200'].iloc[-1]
            sma200_prev = df['SMA_200'].iloc[-2]
            
            # Golden Cross
            if sma50_prev <= sma200_prev and sma50_current > sma200_current:
                return {
                    "symbol": symbol,
                    "pattern_type": "GOLDEN_CROSS",
                    "confidence": 0.8,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": "Golden Cross: 50 SMA crossed above 200 SMA",
                    "action": "BUY"
                }
                
            # Death Cross
            if sma50_prev >= sma200_prev and sma50_current < sma200_current:
                return {
                    "symbol": symbol,
                    "pattern_type": "DEATH_CROSS",
                    "confidence": 0.8,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": "Death Cross: 50 SMA crossed below 200 SMA",
                    "action": "SELL"
                }
                
        except Exception as e:
            logger.error(f"Error detecting golden cross for {symbol}: {e}")
            
        return None

    def detect_stddev_atr_pattern(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect StdDev/ATR volatility pattern"""
        try:
            if len(df) < 20:
                return None

            # Set parameters
            std_dev_length = 20
            atr_length = 14

            # Calculate standard deviation of closing prices
            df['std_dev'] = df['Close'].rolling(window=std_dev_length).std()

            # Calculate ATR using ta library
            atr_indicator = ta.volatility.AverageTrueRange(
                high=df['High'],
                low=df['Low'],
                close=df['Close'],
                window=atr_length
            )
            df['atr'] = atr_indicator.average_true_range()

            # Calculate the ratio of 2 * std_dev to ATR
            df['ratio'] = (2 * df['std_dev']) / df['atr']

            # Get current and previous ratios
            current_ratio = df['ratio'].iloc[-1]
            prev_ratio = df['ratio'].iloc[-2] if len(df) > 1 else current_ratio

            # Skip if we don't have valid data
            if pd.isna(current_ratio) or pd.isna(prev_ratio):
                return None

            # Determine pattern type and action based on ratio
            pattern_type = None
            action = "WATCH"
            confidence = 0.5
            description = f"StdDev/ATR ratio: {current_ratio:.2f}"

            # Pattern detection logic
            if current_ratio < 1:
                pattern_type = "STDDEV_ATR_ORANGE"
                action = "BUY"  # Low volatility, potential breakout
                confidence = 0.7
                description = f"Low volatility detected (ratio: {current_ratio:.2f} < 1.0) - Potential breakout setup"

            elif current_ratio < 1.5:
                pattern_type = "STDDEV_ATR_RED"
                action = "WATCH"
                confidence = 0.6
                description = f"Moderate-low volatility (ratio: {current_ratio:.2f}) - Monitor for breakout"

            elif current_ratio < 2:
                pattern_type = "STDDEV_ATR_GRAY"
                action = "WATCH"
                confidence = 0.4
                description = f"Normal volatility range (ratio: {current_ratio:.2f})"

            elif current_ratio > 4:
                pattern_type = "STDDEV_ATR_YELLOW"
                action = "SELL"  # High volatility, potential reversal
                confidence = 0.8
                description = f"Extreme volatility detected (ratio: {current_ratio:.2f} > 4.0) - Potential reversal"

            else:  # 2 <= ratio <= 4
                pattern_type = "STDDEV_ATR_GREEN"
                action = "WATCH"
                confidence = 0.5
                description = f"Elevated volatility (ratio: {current_ratio:.2f}) - Normal range"

            # Only return pattern if it's significant or newly detected
            if pattern_type and (current_ratio < 1 or current_ratio > 4 or abs(current_ratio - prev_ratio) > 0.5):
                return {
                    "symbol": symbol,
                    "pattern_type": pattern_type,
                    "confidence": confidence,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": description,
                    "action": action,
                    "ratio": current_ratio,
                    "std_dev": df['std_dev'].iloc[-1],
                    "atr": df['atr'].iloc[-1]
                }

        except Exception as e:
            logger.error(f"Error detecting StdDev/ATR pattern for {symbol}: {e}")

        return None

    def detect_stddev_atr_pattern(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Detect StdDev/ATR volatility pattern - Custom pattern for brand new detection"""
        try:
            if len(df) < 20:
                return None

            # Set parameters
            std_dev_length = 20
            atr_length = 14

            # Calculate standard deviation of closing prices
            df['std_dev'] = df['Close'].rolling(window=std_dev_length).std()

            # Calculate ATR using ta library
            atr_indicator = ta.volatility.AverageTrueRange(
                high=df['High'],
                low=df['Low'],
                close=df['Close'],
                window=atr_length
            )
            df['atr'] = atr_indicator.average_true_range()

            # Calculate the ratio of 2 * std_dev to ATR
            df['ratio'] = (2 * df['std_dev']) / df['atr']

            # Get current and previous ratios
            current_ratio = df['ratio'].iloc[-1]
            prev_ratio = df['ratio'].iloc[-2] if len(df) > 1 else current_ratio

            # Skip if we don't have valid data
            if pd.isna(current_ratio) or pd.isna(prev_ratio):
                return None

            # Determine pattern type and action based on ratio
            pattern_type = None
            action = "WATCH"
            confidence = 0.5
            description = f"StdDev/ATR ratio: {current_ratio:.2f}"

            # Pattern detection logic based on your color scheme
            if current_ratio < 1:
                pattern_type = "STDDEV_ATR_ORANGE"
                action = "BUY"  # Low volatility, potential breakout setup
                confidence = 0.8
                description = f"🟠 LOW VOLATILITY ALERT: {symbol} ratio {current_ratio:.2f} < 1.0 - Potential breakout setup!"

            elif current_ratio < 1.5:
                pattern_type = "STDDEV_ATR_RED"
                action = "WATCH"
                confidence = 0.6
                description = f"🔴 Moderate-low volatility: {symbol} ratio {current_ratio:.2f} - Monitor for breakout"

            elif current_ratio < 2:
                pattern_type = "STDDEV_ATR_GRAY"
                action = "WATCH"
                confidence = 0.4
                description = f"⚫ Normal volatility: {symbol} ratio {current_ratio:.2f}"

            elif current_ratio > 4:
                pattern_type = "STDDEV_ATR_YELLOW"
                action = "SELL"  # High volatility, potential reversal/exit
                confidence = 0.9
                description = f"🟡 EXTREME VOLATILITY ALERT: {symbol} ratio {current_ratio:.2f} > 4.0 - Potential reversal!"

            else:  # 2 <= ratio <= 4
                pattern_type = "STDDEV_ATR_GREEN"
                action = "WATCH"
                confidence = 0.5
                description = f"🟢 Elevated volatility: {symbol} ratio {current_ratio:.2f} - Normal range"

            # Only return pattern if it's significant (brand new detection criteria)
            significant_change = abs(current_ratio - prev_ratio) > 0.3
            extreme_values = current_ratio < 1 or current_ratio > 4

            if pattern_type and (extreme_values or significant_change):
                return {
                    "symbol": symbol,
                    "pattern_type": pattern_type,
                    "confidence": confidence,
                    "price": df['Close'].iloc[-1],
                    "volume": df['Volume'].iloc[-1],
                    "description": description,
                    "action": action,
                    "ratio": current_ratio,
                    "prev_ratio": prev_ratio,
                    "std_dev": df['std_dev'].iloc[-1],
                    "atr": df['atr'].iloc[-1],
                    "is_brand_new": extreme_values or significant_change
                }

        except Exception as e:
            logger.error(f"Error detecting StdDev/ATR pattern for {symbol}: {e}")

        return None

    async def analyze_symbol(self, df: pd.DataFrame, symbol: str) -> List[Dict]:
        """Analyze a symbol for all patterns"""
        patterns = []

        # Calculate technical indicators
        df = self.calculate_technical_indicators(df)

        # Detect various patterns
        pattern_methods = [
            self.detect_breakout_pattern,
            self.detect_rsi_patterns,
            self.detect_volume_spike,
            self.detect_macd_crossover,
            self.detect_golden_cross,
            self.detect_stddev_atr_pattern  # Add our new pattern
        ]

        for method in pattern_methods:
            try:
                pattern = method(df, symbol)
                if pattern:
                    patterns.append(pattern)
            except Exception as e:
                logger.error(f"Error in pattern detection method {method.__name__}: {e}")

        return patterns
