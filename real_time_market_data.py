#!/usr/bin/env python3
"""
Real-Time Market Data Integration
Provides live stock prices, news, and market data for Trump AI
"""

import requests
import json
import time
from datetime import datetime
import yfinance as yf
import pandas as pd

class RealTimeMarketData:
    """Real-time market data provider"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 60  # Cache for 1 minute
        
    def get_stock_price(self, symbol):
        """Get current stock price"""
        try:
            # Check cache first
            cache_key = f"price_{symbol}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            # Get live data from Yahoo Finance
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Get current price
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            previous_close = info.get('previousClose')
            
            if current_price and previous_close:
                change = current_price - previous_close
                change_percent = (change / previous_close) * 100
                
                price_data = {
                    'symbol': symbol,
                    'current_price': current_price,
                    'previous_close': previous_close,
                    'change': change,
                    'change_percent': change_percent,
                    'timestamp': datetime.now()
                }
                
                # Cache the result
                self._cache_data(cache_key, price_data)
                return price_data
            
            return None
            
        except Exception as e:
            print(f"Error getting price for {symbol}: {e}")
            return None
    
    def get_stock_info(self, symbol):
        """Get detailed stock information"""
        try:
            cache_key = f"info_{symbol}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            stock_info = {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'dividend_yield': info.get('dividendYield'),
                'volume': info.get('volume'),
                'avg_volume': info.get('averageVolume'),
                '52_week_high': info.get('fiftyTwoWeekHigh'),
                '52_week_low': info.get('fiftyTwoWeekLow'),
                'timestamp': datetime.now()
            }
            
            self._cache_data(cache_key, stock_info)
            return stock_info
            
        except Exception as e:
            print(f"Error getting info for {symbol}: {e}")
            return None
    
    def get_market_news(self, symbol=None):
        """Get latest market news"""
        try:
            if symbol:
                ticker = yf.Ticker(symbol)
                news = ticker.news[:3]  # Get top 3 news items
            else:
                # Get general market news (using SPY as proxy)
                ticker = yf.Ticker("SPY")
                news = ticker.news[:5]
            
            news_items = []
            for item in news:
                news_items.append({
                    'title': item.get('title', ''),
                    'publisher': item.get('publisher', ''),
                    'link': item.get('link', ''),
                    'published': datetime.fromtimestamp(item.get('providerPublishTime', 0))
                })
            
            return news_items
            
        except Exception as e:
            print(f"Error getting news: {e}")
            return []
    
    def get_market_indices(self):
        """Get major market indices"""
        try:
            indices = {
                'SPY': 'S&P 500',
                'QQQ': 'NASDAQ',
                'DIA': 'Dow Jones'
            }
            
            results = {}
            for symbol, name in indices.items():
                price_data = self.get_stock_price(symbol)
                if price_data:
                    results[name] = price_data
            
            return results
            
        except Exception as e:
            print(f"Error getting indices: {e}")
            return {}
    
    def calculate_stddev_atr_ratio(self, symbol, period=20):
        """Calculate actual StdDev/ATR ratio using real data"""
        try:
            # Get historical data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="2mo")  # Get 2 months of data
            
            if len(hist) < period:
                return None
            
            # Calculate Standard Deviation of closing prices
            std_dev = hist['Close'].rolling(window=period).std().iloc[-1]
            
            # Calculate ATR (Average True Range)
            high_low = hist['High'] - hist['Low']
            high_close = abs(hist['High'] - hist['Close'].shift())
            low_close = abs(hist['Low'] - hist['Close'].shift())
            
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean().iloc[-1]
            
            if atr > 0:
                ratio = (2 * std_dev) / atr
                return {
                    'symbol': symbol,
                    'ratio': ratio,
                    'std_dev': std_dev,
                    'atr': atr,
                    'timestamp': datetime.now()
                }
            
            return None
            
        except Exception as e:
            print(f"Error calculating StdDev/ATR for {symbol}: {e}")
            return None
    
    def get_crypto_price(self, symbol):
        """Get cryptocurrency price"""
        try:
            # Add -USD suffix for crypto
            crypto_symbol = f"{symbol}-USD"
            return self.get_stock_price(crypto_symbol)
            
        except Exception as e:
            print(f"Error getting crypto price for {symbol}: {e}")
            return None
    
    def _is_cached(self, key):
        """Check if data is cached and still valid"""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]['timestamp']
        return (time.time() - cache_time) < self.cache_timeout
    
    def _cache_data(self, key, data):
        """Cache data with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    def format_price(self, price):
        """Format price for display"""
        if price is None:
            return "N/A"
        return f"${price:.2f}"
    
    def format_change(self, change, change_percent):
        """Format price change for display"""
        if change is None or change_percent is None:
            return "N/A"
        
        sign = "+" if change >= 0 else ""
        return f"{sign}{change:.2f} ({sign}{change_percent:.2f}%)"
    
    def format_market_cap(self, market_cap):
        """Format market cap for display"""
        if market_cap is None:
            return "N/A"
        
        if market_cap >= 1e12:
            return f"${market_cap/1e12:.2f}T"
        elif market_cap >= 1e9:
            return f"${market_cap/1e9:.2f}B"
        elif market_cap >= 1e6:
            return f"${market_cap/1e6:.2f}M"
        else:
            return f"${market_cap:,.0f}"

# Global market data instance
market_data = RealTimeMarketData()

def test_market_data():
    """Test the market data functionality"""
    print("🧪 Testing Real-Time Market Data...")
    
    # Test stock price
    aapl_price = market_data.get_stock_price("AAPL")
    if aapl_price:
        print(f"✅ AAPL: {market_data.format_price(aapl_price['current_price'])}")
        print(f"   Change: {market_data.format_change(aapl_price['change'], aapl_price['change_percent'])}")
    
    # Test StdDev/ATR ratio
    aapl_ratio = market_data.calculate_stddev_atr_ratio("AAPL")
    if aapl_ratio:
        print(f"📊 AAPL StdDev/ATR Ratio: {aapl_ratio['ratio']:.3f}")
    
    # Test market indices
    indices = market_data.get_market_indices()
    for name, data in indices.items():
        print(f"📈 {name}: {market_data.format_price(data['current_price'])}")
    
    print("🎉 Market data test complete!")

if __name__ == "__main__":
    test_market_data()
