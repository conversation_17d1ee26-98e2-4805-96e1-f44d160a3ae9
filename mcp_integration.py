#!/usr/bin/env python3
"""
MCP Integration Layer
Connects our trading bot with the Alpaca MCP server for professional-grade trading
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# Import MCP server functions
from alpaca_mcp_server import (
    get_account_info, get_positions, get_stock_quote, get_stock_bars,
    place_stock_order, get_orders, cancel_order_by_id, get_market_clock,
    close_position, get_asset_info
)

# Import our existing modules
from analysis.pattern_detector import PatternDetector
from notifications.alert_system import alert_system
from ai.openai_integration import ai_analyst

logger = logging.getLogger(__name__)

class MCPTradingEngine:
    """Enhanced trading engine using Alpaca MCP server"""
    
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.active_strategies = {}
        self.trade_history = []
        self.risk_limits = {
            'max_position_size': 1000.0,
            'max_daily_trades': 10,
            'risk_per_trade': 0.02,
            'max_drawdown': 0.10
        }
        self.daily_trades = 0
        self.is_running = False
        
    async def initialize(self) -> bool:
        """Initialize the MCP trading engine"""
        try:
            # Get account info using MCP server
            account_info = await get_account_info()
            logger.info(f"MCP Trading Engine initialized")
            logger.info(account_info)
            
            # Check market status
            market_status = await get_market_clock()
            logger.info(market_status)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize MCP trading engine: {e}")
            return False

    async def get_enhanced_market_data(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """Get enhanced market data using MCP server"""
        try:
            # Get historical bars using MCP server
            bars_data = await get_stock_bars(symbol, days)
            
            # Get latest quote
            quote_data = await get_stock_quote(symbol)
            
            # Parse the data (MCP returns formatted strings, we need to extract data)
            # For now, we'll use our existing data source but this shows the integration point
            logger.info(f"MCP Market Data for {symbol}:")
            logger.info(bars_data)
            logger.info(quote_data)
            
            # Return empty DataFrame for now - in production you'd parse the MCP data
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()

    async def execute_ttm_squeeze_strategy(self, symbol: str) -> Optional[Dict]:
        """Execute TTM Squeeze strategy using MCP infrastructure"""
        try:
            logger.info(f"🎯 Analyzing TTM Squeeze for {symbol}")
            
            # Get market data
            bars_data = await get_stock_bars(symbol, 30)
            quote_data = await get_stock_quote(symbol)
            
            # For demonstration, let's create a mock analysis
            # In production, you'd parse the MCP data and run your TTM analysis
            
            squeeze_signal = {
                'symbol': symbol,
                'strategy': 'TTM_SQUEEZE',
                'signal': 'BUY',  # This would come from your TTM analysis
                'confidence': 0.85,
                'entry_price': 150.0,  # This would come from quote data
                'stop_loss': 145.0,
                'take_profit': 160.0,
                'timestamp': datetime.now()
            }
            
            # Execute trade if signal is strong
            if squeeze_signal['confidence'] > 0.8:
                trade_result = await self.execute_trade(squeeze_signal)
                return trade_result
                
            return squeeze_signal
            
        except Exception as e:
            logger.error(f"Error in TTM Squeeze strategy for {symbol}: {e}")
            return None

    async def execute_stddev_atr_strategy(self, symbol: str) -> Optional[Dict]:
        """Execute StdDev/ATR strategy using MCP infrastructure"""
        try:
            logger.info(f"📊 Analyzing StdDev/ATR for {symbol}")
            
            # Get historical data for analysis
            bars_data = await get_stock_bars(symbol, 30)
            
            # This would integrate with your existing StdDev/ATR pattern detector
            # For now, creating a mock signal
            
            volatility_signal = {
                'symbol': symbol,
                'strategy': 'STDDEV_ATR',
                'pattern_type': 'STDDEV_ATR_ORANGE',
                'ratio': 0.85,  # This would come from your calculation
                'signal': 'BUY',
                'confidence': 0.75,
                'entry_price': 150.0,
                'timestamp': datetime.now()
            }
            
            # Send special alert for StdDev/ATR patterns
            await alert_system.send_stddev_atr_alert(volatility_signal)
            
            return volatility_signal
            
        except Exception as e:
            logger.error(f"Error in StdDev/ATR strategy for {symbol}: {e}")
            return None

    async def execute_trade(self, signal: Dict) -> Optional[Dict]:
        """Execute trade using MCP server with enhanced risk management"""
        try:
            symbol = signal['symbol']
            side = signal['signal'].lower()
            entry_price = signal['entry_price']
            
            # Check risk limits
            if not await self.check_risk_limits(symbol, entry_price):
                logger.warning(f"Trade rejected due to risk limits: {symbol}")
                return None
            
            # Calculate position size
            position_size = await self.calculate_position_size(symbol, entry_price)
            
            if position_size <= 0:
                logger.warning(f"Invalid position size for {symbol}")
                return None
            
            # Place order using MCP server
            order_result = await place_stock_order(
                symbol=symbol,
                side=side,
                quantity=position_size,
                order_type="market",
                time_in_force="day"
            )
            
            logger.info(f"🚀 Trade executed via MCP: {order_result}")
            
            # Set stop loss if specified
            if 'stop_loss' in signal and signal['stop_loss']:
                stop_order = await place_stock_order(
                    symbol=symbol,
                    side="sell" if side == "buy" else "buy",
                    quantity=position_size,
                    order_type="stop",
                    stop_price=signal['stop_loss']
                )
                logger.info(f"🛡️ Stop loss set: {stop_order}")
            
            # Record trade
            trade_record = {
                'symbol': symbol,
                'strategy': signal.get('strategy', 'UNKNOWN'),
                'side': side,
                'quantity': position_size,
                'entry_price': entry_price,
                'timestamp': datetime.now(),
                'mcp_order_result': order_result
            }
            
            self.trade_history.append(trade_record)
            self.daily_trades += 1
            
            # Send trade alert
            await alert_system.send_trade_alert(trade_record)
            
            return trade_record
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    async def check_risk_limits(self, symbol: str, price: float) -> bool:
        """Enhanced risk management using MCP account data"""
        try:
            # Get current account info
            account_info = await get_account_info()
            
            # Get current positions
            positions_info = await get_positions()
            
            # Check daily trade limit
            if self.daily_trades >= self.risk_limits['max_daily_trades']:
                logger.warning("Daily trade limit reached")
                return False
            
            # Check position size limit
            position_value = price * self.calculate_shares(symbol, price)
            if position_value > self.risk_limits['max_position_size']:
                logger.warning(f"Position size too large: ${position_value}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return False

    async def calculate_position_size(self, symbol: str, price: float) -> int:
        """Calculate position size using MCP account data"""
        try:
            # Get account info to determine buying power
            account_info = await get_account_info()
            
            # For now, use a simple calculation
            # In production, you'd parse the account_info string to get actual buying power
            max_position_value = self.risk_limits['max_position_size']
            shares = int(max_position_value / price)
            
            return max(1, shares)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    def calculate_shares(self, symbol: str, price: float) -> int:
        """Helper method to calculate shares"""
        return int(self.risk_limits['max_position_size'] / price)

    async def monitor_positions(self):
        """Monitor positions using MCP server"""
        try:
            # Get current positions
            positions = await get_positions()
            logger.info("📈 Current Positions:")
            logger.info(positions)
            
            # Get recent orders
            orders = await get_orders("all", 10)
            logger.info("📋 Recent Orders:")
            logger.info(orders)
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")

    async def run_continuous_strategy(self, symbols: List[str]):
        """Run continuous trading strategies"""
        self.is_running = True
        logger.info(f"🤖 Starting continuous strategy execution for {len(symbols)} symbols")
        
        while self.is_running:
            try:
                # Check market status
                market_status = await get_market_clock()
                
                for symbol in symbols:
                    if not self.is_running:
                        break
                    
                    # Run TTM Squeeze strategy
                    ttm_result = await self.execute_ttm_squeeze_strategy(symbol)
                    if ttm_result:
                        logger.info(f"TTM Signal: {ttm_result}")
                    
                    # Run StdDev/ATR strategy
                    volatility_result = await self.execute_stddev_atr_strategy(symbol)
                    if volatility_result:
                        logger.info(f"Volatility Signal: {volatility_result}")
                    
                    # Small delay between symbols
                    await asyncio.sleep(1)
                
                # Monitor positions
                await self.monitor_positions()
                
                # Wait before next cycle
                await asyncio.sleep(30)  # 30 second cycle
                
            except Exception as e:
                logger.error(f"Error in continuous strategy: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    def stop(self):
        """Stop the trading engine"""
        self.is_running = False
        logger.info("🛑 MCP Trading Engine stopped")

    async def get_performance_summary(self) -> Dict:
        """Get trading performance summary"""
        try:
            # Get account info
            account_info = await get_account_info()
            
            # Get positions
            positions = await get_positions()
            
            # Calculate basic metrics
            total_trades = len(self.trade_history)
            
            summary = {
                'total_trades': total_trades,
                'daily_trades': self.daily_trades,
                'account_info': account_info,
                'positions': positions,
                'active_strategies': list(self.active_strategies.keys()),
                'last_updated': datetime.now()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}

# Global MCP trading engine instance
mcp_engine = MCPTradingEngine()
