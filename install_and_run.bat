@echo off
echo ========================================
echo    AUTONOMOUS TRADING BOT INSTALLER
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed!
    echo.
    echo Please follow these steps:
    echo 1. Go to: https://www.python.org/downloads/
    echo 2. Download Python 3.11 or newer
    echo 3. During installation, CHECK "Add Python to PATH"
    echo 4. Restart your computer after installation
    echo 5. Run this script again
    echo.
    echo Opening Python download page...
    start https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

echo Installing trading bot dependencies...
echo This may take a few minutes...
echo.

python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo Failed to upgrade pip!
    pause
    exit /b 1
)

python -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install dependencies!
    echo.
    echo Trying alternative installation...
    python -m pip install --user -r requirements.txt
    if %errorlevel% neq 0 (
        echo Installation failed! Please check your internet connection.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo    TESTING SYSTEM CONNECTIONS
echo ========================================
echo.

python test_system.py
if %errorlevel% neq 0 (
    echo.
    echo Some tests failed, but you can still try running the app.
    echo Check the logs for more details.
    echo.
)

echo.
echo ========================================
echo    LAUNCHING DESKTOP APPLICATION
echo ========================================
echo.

echo Starting the Trading Bot Desktop Application...
echo The GUI window should open shortly.
echo.
echo Your API keys are pre-configured:
echo - OpenAI: Ready for AI analysis
echo - Alpaca: Ready for paper trading  
echo - FMP: Ready for market data
echo.
echo SAFETY: Paper trading mode is enabled (no real money at risk)
echo.

python desktop_app.py

if %errorlevel% neq 0 (
    echo.
    echo Application failed to start!
    echo Check the error messages above.
    echo.
    pause
)

echo.
echo Application closed.
pause
