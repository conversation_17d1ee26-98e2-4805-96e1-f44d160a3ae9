# 🚀 Quick Start Guide - Desktop App

## Prerequisites
1. **Python 3.8+** - Download from [python.org](https://www.python.org/downloads/)
   - ⚠️ **IMPORTANT**: Check "Add Python to PATH" during installation
2. **Your API keys are already configured** ✅

## Installation (Windows)

### Option 1: Automatic Setup (Recommended)
1. Double-click `setup.bat`
2. Wait for installation to complete
3. Double-click `start_desktop.bat` or `start_bot.bat`

### Option 2: Manual Setup
```bash
# Install dependencies
python -m pip install -r requirements.txt

# Test the system
python test_system.py

# Start the desktop app
python run_bot.py desktop
```

## First Run

1. **Test System**: `python test_system.py`
   - Verifies all API connections
   - Checks configuration
   - Tests all components

2. **Start Desktop App**: `python run_bot.py desktop`
   - Launches modern desktop GUI
   - All-in-one trading interface

## Desktop Application

The desktop app provides a comprehensive trading interface:

**Main Features:**
- 🖥️ **Modern GUI** - Professional dark theme interface
- 📊 **Real-time Dashboard** - Account balance, P&L, and status
- 🎯 **Live Pattern Alerts** - Real-time pattern detection display
- 📈 **Trade Management** - Complete trade history and monitoring
- 💼 **Position Tracking** - Current positions with P&L
- 📝 **Live Logs** - Real-time system activity
- ⚙️ **Settings Panel** - Configure trading parameters

## Commands

```bash
# Show help
python run_bot.py help

# Test connections
python run_bot.py test

# Check status
python run_bot.py status

# Run bot only
python run_bot.py bot

# Run dashboard only  
python run_bot.py dashboard

# Run desktop app (recommended)
python run_bot.py desktop

# Run both console + web
python run_bot.py both
```

## Safety Features ✅

- **Paper Trading Mode**: No real money at risk
- **Position Limits**: Max $1,000 per trade
- **Daily Limits**: Max 10 trades per day
- **Risk Management**: 2% risk per trade
- **Stop Losses**: Automatic risk controls

## What the Bot Does

### 🔍 Continuous Scanning
- Monitors 20+ stocks every 30 seconds
- Detects breakouts, volume spikes, RSI signals
- Analyzes market movers and new listings

### 🧠 AI Analysis  
- Uses GPT-4 to analyze each pattern
- Calculates risk/reward ratios
- Provides trade recommendations

### 🔔 Smart Notifications
- **Voice alerts** for important patterns
- **Desktop notifications** for all signals
- **Emergency interruptions** for critical events

### 📊 Pattern Detection
- **Breakouts**: Price breaks resistance/support
- **RSI Signals**: Oversold/overbought reversals  
- **Volume Spikes**: Unusual institutional activity
- **MACD Crossovers**: Momentum shifts
- **Golden Cross**: Long-term trend changes

## Troubleshooting

### Python Not Found
- Install Python from python.org
- Make sure "Add to PATH" was checked
- Restart command prompt

### API Connection Errors
- Check internet connection
- Verify API keys in config/settings.py
- Run `python run_bot.py test`

### Permission Errors
- Run as Administrator (Windows)
- Check antivirus settings
- Ensure Python has network access

## Next Steps

1. **Monitor Dashboard**: Watch for pattern alerts
2. **Review Trades**: Check trade history and performance  
3. **Adjust Settings**: Modify risk parameters if needed
4. **Scale Up**: Increase position sizes when comfortable

## Support

- Check `trading_bot.log` for detailed logs
- Run `python test_system.py` to diagnose issues
- Use `python run_bot.py status` for system overview

---

**Ready to start? Run `python run_bot.py desktop` or double-click `start_desktop.bat`** 🚀
