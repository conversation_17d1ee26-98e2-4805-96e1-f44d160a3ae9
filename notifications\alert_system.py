import pyttsx3
import asyncio
from plyer import notification
import logging
from datetime import datetime
from typing import Dict, List
from config.settings import settings

logger = logging.getLogger(__name__)

class AlertSystem:
    def __init__(self):
        self.tts_engine = None
        self.initialize_tts()
        
    def initialize_tts(self):
        """Initialize text-to-speech engine"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure voice settings
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to use a female voice if available
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                        
            self.tts_engine.setProperty('rate', settings.VOICE_RATE)
            self.tts_engine.setProperty('volume', settings.VOICE_VOLUME)
            
            logger.info("Text-to-speech engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            self.tts_engine = None

    async def speak_alert(self, message: str, priority: str = "MEDIUM"):
        """Convert text to speech for audio alerts"""
        if not settings.ENABLE_VOICE_ALERTS or not self.tts_engine:
            return
            
        try:
            # Add priority prefix for urgent alerts
            if priority == "CRITICAL":
                message = f"URGENT ALERT! {message}"
            elif priority == "HIGH":
                message = f"Important: {message}"
                
            # Run TTS in a separate thread to avoid blocking
            def speak():
                try:
                    self.tts_engine.say(message)
                    self.tts_engine.runAndWait()
                except Exception as e:
                    logger.error(f"Error in TTS: {e}")
                    
            await asyncio.get_event_loop().run_in_executor(None, speak)
            logger.info(f"Voice alert delivered: {message[:50]}...")
            
        except Exception as e:
            logger.error(f"Error in speak_alert: {e}")

    def show_desktop_notification(self, title: str, message: str, timeout: int = 10):
        """Show desktop notification"""
        if not settings.ENABLE_DESKTOP_NOTIFICATIONS:
            return
            
        try:
            notification.notify(
                title=title,
                message=message,
                timeout=timeout,
                app_name="Trading Bot"
            )
            logger.info(f"Desktop notification sent: {title}")
            
        except Exception as e:
            logger.error(f"Error showing desktop notification: {e}")

    async def send_pattern_alert(self, pattern: Dict):
        """Send alert for detected pattern"""
        try:
            symbol = pattern.get('symbol', 'Unknown')
            pattern_type = pattern.get('pattern_type', 'Unknown')
            confidence = pattern.get('confidence', 0)
            price = pattern.get('price', 0)
            action = pattern.get('action', 'WATCH')
            description = pattern.get('description', '')

            # Create alert message
            title = f"Pattern Detected: {symbol}"
            message = f"{pattern_type} pattern detected for {symbol} at ${price:.2f}. Confidence: {confidence*100:.0f}%. Action: {action}"

            if description:
                message += f"\nDetails: {description}"

            # Special handling for StdDev/ATR patterns
            if "STDDEV_ATR" in pattern_type:
                await self.send_stddev_atr_alert(pattern)
                return

            # Determine priority based on confidence and action
            if confidence >= 0.8 and action in ['BUY', 'SELL']:
                priority = "HIGH"
            elif confidence >= 0.6:
                priority = "MEDIUM"
            else:
                priority = "LOW"

            # Send desktop notification
            self.show_desktop_notification(title, message)

            # Send voice alert for high priority patterns
            if priority in ["HIGH", "CRITICAL"]:
                voice_message = f"Trading alert for {symbol}. {pattern_type} pattern detected. Recommended action: {action}. Price: {price:.2f} dollars."
                await self.speak_alert(voice_message, priority)

            logger.info(f"Pattern alert sent for {symbol}: {pattern_type}")

        except Exception as e:
            logger.error(f"Error sending pattern alert: {e}")

    async def send_stddev_atr_alert(self, pattern: Dict):
        """Special alert for StdDev/ATR pattern - Chat-style notification"""
        try:
            symbol = pattern.get('symbol', 'Unknown')
            pattern_type = pattern.get('pattern_type', 'Unknown')
            ratio = pattern.get('ratio', 0)
            prev_ratio = pattern.get('prev_ratio', 0)
            price = pattern.get('price', 0)
            action = pattern.get('action', 'WATCH')
            description = pattern.get('description', '')
            is_brand_new = pattern.get('is_brand_new', False)

            # Create chat-style message
            chat_message = f"""
🚨 STDDEV/ATR PATTERN ALERT 🚨

Symbol: {symbol}
Current Price: ${price:.2f}
Pattern: {pattern_type.replace('STDDEV_ATR_', '')}
Ratio: {ratio:.3f} (Previous: {prev_ratio:.3f})
Action: {action}

{description}

{'🆕 BRAND NEW PATTERN DETECTED!' if is_brand_new else ''}
            """.strip()

            # Determine urgency
            if ratio < 1 or ratio > 4:
                priority = "CRITICAL"
                title = f"🚨 EXTREME VOLATILITY: {symbol}"
            elif abs(ratio - prev_ratio) > 0.5:
                priority = "HIGH"
                title = f"⚡ VOLATILITY CHANGE: {symbol}"
            else:
                priority = "MEDIUM"
                title = f"📊 VOLATILITY PATTERN: {symbol}"

            # Send multiple notification types for brand new patterns
            if is_brand_new:
                # Desktop notification
                self.show_desktop_notification(title, chat_message[:200] + "...")

                # Voice alert
                voice_msg = f"Brand new volatility pattern detected for {symbol}. "
                if ratio < 1:
                    voice_msg += f"Low volatility ratio of {ratio:.2f}. Potential breakout setup."
                elif ratio > 4:
                    voice_msg += f"Extreme volatility ratio of {ratio:.2f}. Potential reversal signal."
                else:
                    voice_msg += f"Significant volatility change. Ratio now {ratio:.2f}."

                await self.speak_alert(voice_msg, priority)

                # Log as chat message
                logger.info(f"CHAT ALERT:\n{chat_message}")

                # Print to console for immediate visibility
                print("\n" + "="*60)
                print("🤖 TRADING BOT CHAT ALERT")
                print("="*60)
                print(chat_message)
                print("="*60 + "\n")

            # Always log the pattern
            logger.info(f"StdDev/ATR pattern detected for {symbol}: {pattern_type}, ratio: {ratio:.3f}")

        except Exception as e:
            logger.error(f"Error sending StdDev/ATR alert: {e}")

    async def send_trade_alert(self, trade_info: Dict):
        """Send alert for executed trade"""
        try:
            symbol = trade_info.get('symbol', 'Unknown')
            side = trade_info.get('side', 'Unknown')
            quantity = trade_info.get('quantity', 0)
            price = trade_info.get('filled_avg_price', 0)
            status = trade_info.get('status', 'Unknown')
            
            title = f"Trade Executed: {symbol}"
            message = f"{side} {quantity} shares of {symbol} at ${price:.2f}. Status: {status}"
            
            # Send desktop notification
            self.show_desktop_notification(title, message)
            
            # Send voice alert
            voice_message = f"Trade executed. {side} {quantity} shares of {symbol} at {price:.2f} dollars."
            await self.speak_alert(voice_message, "HIGH")
            
            logger.info(f"Trade alert sent for {symbol}: {side} {quantity} @ ${price:.2f}")
            
        except Exception as e:
            logger.error(f"Error sending trade alert: {e}")

    async def send_market_alert(self, alert_type: str, message: str, priority: str = "MEDIUM"):
        """Send general market alert"""
        try:
            title = f"Market Alert: {alert_type}"
            
            # Send desktop notification
            self.show_desktop_notification(title, message)
            
            # Send voice alert for important market events
            if priority in ["HIGH", "CRITICAL"]:
                await self.speak_alert(f"Market alert: {message}", priority)
            
            logger.info(f"Market alert sent: {alert_type}")
            
        except Exception as e:
            logger.error(f"Error sending market alert: {e}")

    async def send_system_alert(self, message: str, priority: str = "LOW"):
        """Send system status alert"""
        try:
            title = "Trading Bot System"
            
            # Send desktop notification for important system events
            if priority in ["HIGH", "CRITICAL"]:
                self.show_desktop_notification(title, message)
                await self.speak_alert(f"System alert: {message}", priority)
            
            logger.info(f"System alert: {message}")
            
        except Exception as e:
            logger.error(f"Error sending system alert: {e}")

    async def send_earnings_alert(self, earnings_data: List[Dict]):
        """Send alert for upcoming earnings"""
        try:
            if not earnings_data:
                return
                
            # Focus on earnings today or tomorrow
            today_earnings = [e for e in earnings_data if e.get('date') == datetime.now().strftime('%Y-%m-%d')]
            
            if today_earnings:
                symbols = [e.get('symbol', '') for e in today_earnings[:5]]  # Top 5
                message = f"Earnings today: {', '.join(symbols)}"
                
                self.show_desktop_notification("Earnings Alert", message)
                
                if len(today_earnings) >= 3:  # Only voice alert if significant
                    await self.speak_alert(f"Multiple earnings reports today including {', '.join(symbols[:3])}")
            
        except Exception as e:
            logger.error(f"Error sending earnings alert: {e}")

    async def send_new_listing_alert(self, new_listings: List[Dict]):
        """Send alert for new stock listings"""
        try:
            if not new_listings:
                return
                
            recent_listings = [listing for listing in new_listings if listing.get('symbol')]
            
            if recent_listings:
                symbols = [listing.get('symbol', '') for listing in recent_listings[:3]]
                message = f"New listings detected: {', '.join(symbols)}"
                
                self.show_desktop_notification("New Listings", message)
                await self.speak_alert(f"New stock listings detected: {', '.join(symbols)}", "MEDIUM")
            
        except Exception as e:
            logger.error(f"Error sending new listing alert: {e}")

    async def interrupt_user(self, urgent_message: str):
        """Interrupt user with urgent alert (highest priority)"""
        try:
            # Flash desktop notification multiple times
            for i in range(3):
                self.show_desktop_notification("URGENT TRADING ALERT", urgent_message, timeout=15)
                await asyncio.sleep(1)
            
            # Speak alert multiple times
            for i in range(2):
                await self.speak_alert(f"URGENT! {urgent_message}", "CRITICAL")
                await asyncio.sleep(2)
            
            logger.warning(f"User interrupted with urgent alert: {urgent_message}")
            
        except Exception as e:
            logger.error(f"Error interrupting user: {e}")

# Global alert system instance
alert_system = AlertSystem()
