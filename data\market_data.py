import yfinance as yf
import requests
import pandas as pd
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

class MarketDataProvider:
    def __init__(self):
        self.fmp_base_url = "https://financialmodelingprep.com/api/v3"
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def get_real_time_quote(self, symbol: str) -> Dict:
        """Get real-time quote for a symbol"""
        try:
            # Using yfinance for real-time data
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d", interval="1m")
            
            if hist.empty:
                return None
                
            latest = hist.iloc[-1]
            
            return {
                "symbol": symbol,
                "price": float(latest['Close']),
                "volume": int(latest['Volume']),
                "high": float(latest['High']),
                "low": float(latest['Low']),
                "open": float(latest['Open']),
                "timestamp": datetime.now(),
                "market_cap": info.get('marketCap', 0),
                "avg_volume": info.get('averageVolume', 0),
                "sector": info.get('sector', 'Unknown')
            }
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {e}")
            return None

    async def get_historical_data(self, symbol: str, period: str = "1mo") -> pd.DataFrame:
        """Get historical data for technical analysis"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            return hist
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()

    async def get_market_movers(self) -> Dict:
        """Get market movers using FMP API"""
        try:
            url = f"{self.fmp_base_url}/stock_market/gainers"
            params = {"apikey": settings.FMP_API_KEY}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    gainers = await response.json()
                else:
                    gainers = []
            
            url = f"{self.fmp_base_url}/stock_market/losers"
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    losers = await response.json()
                else:
                    losers = []
            
            url = f"{self.fmp_base_url}/stock_market/actives"
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    actives = await response.json()
                else:
                    actives = []
            
            return {
                "gainers": gainers[:10],  # Top 10
                "losers": losers[:10],
                "most_active": actives[:10]
            }
        except Exception as e:
            logger.error(f"Error getting market movers: {e}")
            return {"gainers": [], "losers": [], "most_active": []}

    async def get_new_listings(self) -> List[Dict]:
        """Get newly listed stocks"""
        try:
            # Get IPO calendar for recent listings
            url = f"{self.fmp_base_url}/ipo_calendar"
            params = {
                "apikey": settings.FMP_API_KEY,
                "from": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                "to": datetime.now().strftime("%Y-%m-%d")
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    ipos = await response.json()
                    return ipos
                else:
                    return []
        except Exception as e:
            logger.error(f"Error getting new listings: {e}")
            return []

    async def get_earnings_calendar(self) -> List[Dict]:
        """Get upcoming earnings"""
        try:
            url = f"{self.fmp_base_url}/earning_calendar"
            params = {
                "apikey": settings.FMP_API_KEY,
                "from": datetime.now().strftime("%Y-%m-%d"),
                "to": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    earnings = await response.json()
                    return earnings
                else:
                    return []
        except Exception as e:
            logger.error(f"Error getting earnings calendar: {e}")
            return []

    async def get_news(self, symbol: str = None, limit: int = 10) -> List[Dict]:
        """Get market news"""
        try:
            if symbol:
                url = f"{self.fmp_base_url}/stock_news"
                params = {"tickers": symbol, "limit": limit, "apikey": settings.FMP_API_KEY}
            else:
                url = f"{self.fmp_base_url}/general_news"
                params = {"page": 0, "apikey": settings.FMP_API_KEY}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    news = await response.json()
                    return news[:limit]
                else:
                    return []
        except Exception as e:
            logger.error(f"Error getting news: {e}")
            return []

    async def scan_for_patterns(self, symbols: List[str]) -> List[Dict]:
        """Scan multiple symbols for trading patterns"""
        results = []
        
        for symbol in symbols:
            try:
                quote = await self.get_real_time_quote(symbol)
                if quote:
                    results.append(quote)
                    
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
                
        return results
