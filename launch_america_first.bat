@echo off
color 0C
echo.
echo ========================================
echo    🇺🇸 AMERICA FIRST TRADING SYSTEM 🇺🇸
echo ========================================
echo.
echo    MAKING TRADING GREAT AGAIN!
echo    🦅 EAGLES! 🚀 ROCKETS! 💰 PROFITS! 🦅
echo.
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo TREMENDOUS ERROR: Python not found!
    echo.
    echo We need Python to make AMERICA FIRST trading!
    echo Please install Python from python.org
    echo.
    pause
    exit /b 1
)

echo 🚀 Python found - TREMENDOUS!
python --version
echo.

echo 🦅 Installing AMERICA FIRST dependencies...
echo This is going to be HUGE!
echo.

python -m pip install --upgrade pip
python -m pip install customtkinter pillow matplotlib pandas numpy yfinance openai requests aiohttp

echo.
echo ========================================
echo    🇺🇸 LAUNCHING AMERICA FIRST TRADING 🇺🇸
echo ========================================
echo.
echo 🦅 Starting the most TREMENDOUS trading system!
echo 🚀 Making profits GREAT AGAIN!
echo 💰 WINNING like never before!
echo.
echo Press Ctrl+C to stop WINNING (if you can!)
echo.

python america_first_desktop.py

echo.
echo 🇺🇸 AMERICA FIRST TRADING SESSION COMPLETE! 🇺🇸
echo Thanks for making trading GREAT AGAIN!
echo.
pause
