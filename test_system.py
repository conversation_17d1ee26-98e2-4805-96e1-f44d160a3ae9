#!/usr/bin/env python3
"""
System Test Script
Tests all major components of the trading bot system.
"""

import asyncio
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_market_data():
    """Test market data provider"""
    print("\n🔍 Testing Market Data Provider...")
    try:
        from data.market_data import MarketDataProvider
        
        async with MarketDataProvider() as provider:
            # Test real-time quote
            quote = await provider.get_real_time_quote("AAPL")
            if quote:
                print(f"✓ Real-time quote: AAPL @ ${quote['price']:.2f}")
            else:
                print("✗ Failed to get real-time quote")
                return False
            
            # Test market movers
            movers = await provider.get_market_movers()
            if movers and movers.get('gainers'):
                print(f"✓ Market movers: {len(movers['gainers'])} gainers found")
            else:
                print("✗ Failed to get market movers")
                return False
            
            print("✓ Market data provider working correctly")
            return True
            
    except Exception as e:
        print(f"✗ Market data provider error: {e}")
        return False

async def test_pattern_detection():
    """Test pattern detection"""
    print("\n🎯 Testing Pattern Detection...")
    try:
        from analysis.pattern_detector import PatternDetector
        from data.market_data import MarketDataProvider
        
        detector = PatternDetector()
        
        async with MarketDataProvider() as provider:
            # Get historical data for AAPL
            hist_data = await provider.get_historical_data("AAPL", "1mo")
            
            if hist_data.empty:
                print("✗ No historical data available")
                return False
            
            # Analyze for patterns
            patterns = await detector.analyze_symbol(hist_data, "AAPL")
            
            print(f"✓ Pattern detection working: {len(patterns)} patterns found for AAPL")
            
            for pattern in patterns[:3]:  # Show first 3 patterns
                print(f"  - {pattern['pattern_type']}: {pattern['confidence']*100:.0f}% confidence")
            
            return True
            
    except Exception as e:
        print(f"✗ Pattern detection error: {e}")
        return False

async def test_broker_connection():
    """Test broker connection"""
    print("\n💼 Testing Broker Connection...")
    try:
        from trading.broker_interface import AlpacaBroker
        
        broker = AlpacaBroker()
        success = await broker.initialize()
        
        if not success:
            print("✗ Failed to initialize broker connection")
            return False
        
        # Get account info
        account = broker.get_account_info()
        if account:
            print(f"✓ Broker connected successfully")
            print(f"  - Account Status: {account.get('status', 'Unknown')}")
            print(f"  - Buying Power: ${account.get('buying_power', 0):,.2f}")
            print(f"  - Portfolio Value: ${account.get('portfolio_value', 0):,.2f}")
        else:
            print("✗ Failed to get account info")
            return False
        
        # Test market hours
        market_status = broker.is_market_open()
        print(f"  - Market Open: {'Yes' if market_status else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Broker connection error: {e}")
        return False

async def test_ai_integration():
    """Test OpenAI integration"""
    print("\n🧠 Testing AI Integration...")
    try:
        from ai.openai_integration import OpenAIAnalyst
        
        ai = OpenAIAnalyst()
        
        # Test basic AI request
        response = await ai._make_openai_request("What is 2+2? Respond with just the number.")
        
        if response and "4" in response:
            print("✓ OpenAI integration working correctly")
            
            # Test market sentiment analysis
            sample_news = [
                {"title": "Market reaches new highs", "text": "Stocks continue to rally on positive earnings"},
                {"title": "Tech stocks surge", "text": "Technology sector leads market gains"}
            ]
            
            sentiment = await ai.analyze_market_sentiment(sample_news)
            if sentiment:
                print(f"  - Sentiment analysis: {sentiment.get('sentiment', 'unknown')}")
                print(f"  - Confidence: {sentiment.get('confidence', 0)*100:.0f}%")
            
            return True
        else:
            print(f"✗ Unexpected AI response: {response}")
            return False
            
    except Exception as e:
        print(f"✗ AI integration error: {e}")
        return False

def test_notification_system():
    """Test notification system"""
    print("\n🔔 Testing Notification System...")
    try:
        from notifications.alert_system import alert_system
        
        # Test desktop notification
        alert_system.show_desktop_notification("Test Alert", "System test notification")
        print("✓ Desktop notification sent")
        
        # Test TTS initialization
        if alert_system.tts_engine:
            print("✓ Text-to-speech engine initialized")
        else:
            print("⚠ Text-to-speech engine not available")
        
        return True
        
    except Exception as e:
        print(f"✗ Notification system error: {e}")
        return False

def test_database():
    """Test database connection"""
    print("\n🗄️ Testing Database...")
    try:
        from database.models import create_tables, SessionLocal, Stock
        
        # Create tables
        create_tables()
        print("✓ Database tables created/verified")
        
        # Test database connection
        db = SessionLocal()
        try:
            # Try to query (will create empty result if no data)
            stocks = db.query(Stock).limit(1).all()
            print("✓ Database connection working")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_configuration():
    """Test configuration"""
    print("\n⚙️ Testing Configuration...")
    try:
        from config.settings import settings
        
        # Check required settings
        required_keys = ['OPENAI_API_KEY', 'ALPACA_API_KEY', 'ALPACA_SECRET_KEY', 'FMP_API_KEY']
        
        for key in required_keys:
            value = getattr(settings, key, None)
            if value and len(value) > 10:  # Basic validation
                print(f"✓ {key}: Configured")
            else:
                print(f"✗ {key}: Missing or invalid")
                return False
        
        print(f"✓ Configuration validated")
        print(f"  - Max Position Size: ${settings.MAX_POSITION_SIZE:,.2f}")
        print(f"  - Risk Per Trade: {settings.RISK_PER_TRADE*100:.1f}%")
        print(f"  - Scan Interval: {settings.SCAN_INTERVAL}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

async def run_all_tests():
    """Run all system tests"""
    print("🤖 AUTONOMOUS TRADING BOT - SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("Market Data", test_market_data),
        ("Pattern Detection", test_pattern_detection),
        ("Broker Connection", test_broker_connection),
        ("AI Integration", test_ai_integration),
        ("Notification System", test_notification_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to run.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and try again.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
