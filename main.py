import asyncio
import logging
import signal
import sys
from datetime import datetime, time
from typing import Dict, List
import schedule
from concurrent.futures import ThreadPoolExecutor

# Import our modules
from config.settings import settings
from database.models import create_tables, get_db, Stock, Trade, Pattern, Alert
from data.market_data import MarketDataProvider
from analysis.pattern_detector import PatternDetector
from trading.broker_interface import AlpacaBroker
from notifications.alert_system import alert_system
from ai.openai_integration import ai_analyst

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TradingBot:
    def __init__(self):
        self.market_data = None
        self.pattern_detector = PatternDetector()
        self.broker = AlpacaBroker()
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.daily_trades = 0
        self.last_scan_time = None
        
    async def initialize(self):
        """Initialize all components"""
        try:
            logger.info("Initializing Trading Bot...")
            
            # Create database tables
            create_tables()
            logger.info("Database initialized")
            
            # Initialize broker connection
            broker_connected = await self.broker.initialize()
            if not broker_connected:
                logger.error("Failed to connect to broker")
                return False
            
            # Initialize market data provider
            self.market_data = MarketDataProvider()
            
            # Send startup notification
            await alert_system.send_system_alert("Trading Bot initialized successfully", "HIGH")
            
            logger.info("Trading Bot initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing trading bot: {e}")
            await alert_system.send_system_alert(f"Bot initialization failed: {e}", "CRITICAL")
            return False

    async def scan_market(self):
        """Main market scanning function"""
        try:
            if not self.is_market_open():
                logger.info("Market is closed, skipping scan")
                return
                
            logger.info("Starting market scan...")
            self.last_scan_time = datetime.now()
            
            async with self.market_data as provider:
                # Get market movers and new listings
                market_movers = await provider.get_market_movers()
                new_listings = await provider.get_new_listings()
                
                # Scan watchlist symbols
                watchlist_data = await provider.scan_for_patterns(settings.WATCHLIST_SYMBOLS)
                
                # Combine all symbols to analyze
                all_symbols = []
                
                # Add market movers
                for category in ['gainers', 'losers', 'most_active']:
                    for stock in market_movers.get(category, [])[:5]:
                        if stock.get('symbol'):
                            all_symbols.append(stock['symbol'])
                
                # Add watchlist
                all_symbols.extend(settings.WATCHLIST_SYMBOLS)
                
                # Remove duplicates
                all_symbols = list(set(all_symbols))
                
                # Analyze each symbol for patterns
                detected_patterns = []
                for symbol in all_symbols:
                    try:
                        # Get historical data for technical analysis
                        hist_data = await provider.get_historical_data(symbol, "1mo")
                        if not hist_data.empty:
                            patterns = await self.pattern_detector.analyze_symbol(hist_data, symbol)
                            detected_patterns.extend(patterns)
                            
                        # Small delay to avoid rate limiting
                        await asyncio.sleep(0.2)
                        
                    except Exception as e:
                        logger.error(f"Error analyzing {symbol}: {e}")
                        continue
                
                # Process detected patterns
                await self.process_patterns(detected_patterns)
                
                # Check for new listings
                if new_listings:
                    await alert_system.send_new_listing_alert(new_listings)
                
                # Get earnings calendar
                earnings = await provider.get_earnings_calendar()
                if earnings:
                    await alert_system.send_earnings_alert(earnings)
                
                logger.info(f"Market scan complete. Found {len(detected_patterns)} patterns")
                
        except Exception as e:
            logger.error(f"Error in market scan: {e}")
            await alert_system.send_system_alert(f"Market scan error: {e}", "HIGH")

    async def process_patterns(self, patterns: List[Dict]):
        """Process detected patterns and decide on actions"""
        try:
            for pattern in patterns:
                # Store pattern in database
                await self.store_pattern(pattern)
                
                # Send pattern alert
                await alert_system.send_pattern_alert(pattern)
                
                # Get AI analysis
                symbol = pattern['symbol']
                historical_data = await self.get_symbol_info(symbol)
                ai_analysis = await ai_analyst.analyze_stock_pattern(symbol, pattern, historical_data)
                
                # Decide whether to trade
                should_trade = self.should_execute_trade(pattern, ai_analysis)
                
                if should_trade and self.daily_trades < settings.MAX_DAILY_TRADES:
                    await self.execute_pattern_trade(pattern, ai_analysis)
                    
        except Exception as e:
            logger.error(f"Error processing patterns: {e}")

    def should_execute_trade(self, pattern: Dict, ai_analysis: Dict) -> bool:
        """Determine if we should execute a trade based on pattern and AI analysis"""
        try:
            # Check basic criteria
            confidence = pattern.get('confidence', 0)
            action = pattern.get('action', 'WATCH')
            ai_recommendation = ai_analysis.get('recommendation', 'watch')
            success_probability = ai_analysis.get('success_probability', 0)
            
            # Trading criteria
            if action in ['BUY', 'SELL'] and confidence >= 0.7:
                if ai_recommendation in ['buy', 'sell'] and success_probability >= 60:
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error evaluating trade decision: {e}")
            return False

    async def execute_pattern_trade(self, pattern: Dict, ai_analysis: Dict):
        """Execute trade based on pattern"""
        try:
            symbol = pattern['symbol']
            action = pattern['action']
            price = pattern['price']
            
            # Calculate position size
            position_size = self.broker.calculate_position_size(symbol, price)
            
            if position_size <= 0:
                logger.warning(f"Invalid position size for {symbol}")
                return
            
            # Execute trade
            if action == 'BUY':
                order = await self.broker.place_market_order(symbol, 'buy', position_size)
            elif action == 'SELL':
                # Check if we have position to sell
                positions = self.broker.get_positions()
                has_position = any(pos['symbol'] == symbol and pos['quantity'] > 0 for pos in positions)
                
                if has_position:
                    order = await self.broker.place_market_order(symbol, 'sell', position_size)
                else:
                    logger.info(f"No position to sell for {symbol}")
                    return
            else:
                return
            
            if order:
                # Store trade in database
                await self.store_trade(order, pattern['pattern_type'])
                
                # Send trade alert
                await alert_system.send_trade_alert(order)
                
                # Increment daily trade count
                self.daily_trades += 1
                
                logger.info(f"Trade executed: {action} {position_size} {symbol} @ ${price:.2f}")
                
        except Exception as e:
            logger.error(f"Error executing trade for {symbol}: {e}")

    async def store_pattern(self, pattern: Dict):
        """Store detected pattern in database"""
        try:
            # Implementation would store in database
            logger.info(f"Pattern stored: {pattern['symbol']} - {pattern['pattern_type']}")
        except Exception as e:
            logger.error(f"Error storing pattern: {e}")

    async def store_trade(self, order: Dict, strategy: str):
        """Store executed trade in database"""
        try:
            # Implementation would store in database
            logger.info(f"Trade stored: {order['symbol']} - {order['side']}")
        except Exception as e:
            logger.error(f"Error storing trade: {e}")

    async def get_symbol_info(self, symbol: str) -> Dict:
        """Get additional symbol information"""
        try:
            # Get basic info for AI analysis
            return {
                "high_52w": 0,
                "low_52w": 0,
                "avg_volume": 0,
                "market_cap": 0
            }
        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return {}

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            return self.broker.is_market_open()
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False

    async def run_continuous_scan(self):
        """Run continuous market scanning"""
        self.is_running = True
        logger.info("Starting continuous market scanning...")
        
        while self.is_running:
            try:
                await self.scan_market()
                
                # Wait for next scan
                await asyncio.sleep(settings.SCAN_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in continuous scan: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    def stop(self):
        """Stop the trading bot"""
        logger.info("Stopping trading bot...")
        self.is_running = False

# Global bot instance
bot = TradingBot()

async def main():
    """Main function"""
    try:
        # Initialize bot
        success = await bot.initialize()
        if not success:
            logger.error("Failed to initialize bot")
            return
        
        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal")
            bot.stop()
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start continuous scanning
        await bot.run_continuous_scan()
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        await alert_system.send_system_alert(f"Bot crashed: {e}", "CRITICAL")

if __name__ == "__main__":
    asyncio.run(main())
