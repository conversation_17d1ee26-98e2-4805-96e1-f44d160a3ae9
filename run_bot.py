#!/usr/bin/env python3
"""
Autonomous Trading Bot Launcher
This script provides multiple ways to run the trading bot system.
"""

import asyncio
import sys
import argparse
import logging
from datetime import datetime
import subprocess
import threading
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_main_bot():
    """Run the main trading bot"""
    try:
        logger.info("Starting main trading bot...")
        from main import main
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bo<PERSON> stopped by user")
    except Exception as e:
        logger.error(f"Error running main bot: {e}")

def run_dashboard():
    """Run the web dashboard"""
    try:
        logger.info("Starting web dashboard on http://localhost:8050")
        from web.dashboard import app
        app.run_server(debug=False, host='0.0.0.0', port=8050)
    except Exception as e:
        logger.error(f"Error running dashboard: {e}")

def run_both():
    """Run both bot and dashboard simultaneously"""
    logger.info("Starting both trading bot and dashboard...")
    
    # Start dashboard in a separate thread
    dashboard_thread = threading.Thread(target=run_dashboard, daemon=True)
    dashboard_thread.start()
    
    # Give dashboard time to start
    time.sleep(3)
    
    # Run main bot in current thread
    run_main_bot()

def install_dependencies():
    """Install required dependencies"""
    logger.info("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error installing dependencies: {e}")
        return False
    return True

def setup_environment():
    """Setup environment and check configuration"""
    logger.info("Setting up environment...")
    
    try:
        from config.settings import settings
        
        # Check API keys
        if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "your_openai_key_here":
            logger.error("OpenAI API key not configured")
            return False
            
        if not settings.ALPACA_API_KEY or settings.ALPACA_API_KEY == "your_alpaca_key_here":
            logger.error("Alpaca API key not configured")
            return False
            
        if not settings.FMP_API_KEY or settings.FMP_API_KEY == "your_fmp_key_here":
            logger.error("Financial Modeling Prep API key not configured")
            return False
        
        logger.info("Configuration validated successfully")
        
        # Create database tables
        from database.models import create_tables
        create_tables()
        logger.info("Database initialized")
        
        return True
        
    except Exception as e:
        logger.error(f"Error setting up environment: {e}")
        return False

def test_connections():
    """Test API connections"""
    logger.info("Testing API connections...")
    
    try:
        # Test Alpaca connection
        from trading.broker_interface import AlpacaBroker
        broker = AlpacaBroker()
        
        async def test_broker():
            success = await broker.initialize()
            if success:
                logger.info("✓ Alpaca connection successful")
                account = broker.get_account_info()
                logger.info(f"  Account balance: ${account.get('buying_power', 0):,.2f}")
            else:
                logger.error("✗ Alpaca connection failed")
                return False
            return True
        
        # Test OpenAI connection
        from ai.openai_integration import OpenAIAnalyst
        ai = OpenAIAnalyst()
        
        async def test_openai():
            try:
                response = await ai._make_openai_request("Test connection")
                if response and "error" not in response.lower():
                    logger.info("✓ OpenAI connection successful")
                    return True
                else:
                    logger.error("✗ OpenAI connection failed")
                    return False
            except Exception as e:
                logger.error(f"✗ OpenAI connection failed: {e}")
                return False
        
        # Run tests
        async def run_tests():
            broker_ok = await test_broker()
            openai_ok = await test_openai()
            return broker_ok and openai_ok
        
        success = asyncio.run(run_tests())
        
        if success:
            logger.info("All API connections successful!")
        else:
            logger.error("Some API connections failed. Please check your configuration.")
        
        return success
        
    except Exception as e:
        logger.error(f"Error testing connections: {e}")
        return False

def show_status():
    """Show current system status"""
    print("\n" + "="*60)
    print("🤖 AUTONOMOUS TRADING BOT STATUS")
    print("="*60)
    
    try:
        from config.settings import settings
        
        print(f"📊 Configuration:")
        print(f"   • Max Position Size: ${settings.MAX_POSITION_SIZE:,.2f}")
        print(f"   • Risk Per Trade: {settings.RISK_PER_TRADE*100:.1f}%")
        print(f"   • Max Daily Trades: {settings.MAX_DAILY_TRADES}")
        print(f"   • Scan Interval: {settings.SCAN_INTERVAL}s")
        print(f"   • Watchlist Symbols: {len(settings.WATCHLIST_SYMBOLS)}")
        
        print(f"\n🔑 API Keys:")
        print(f"   • OpenAI: {'✓ Configured' if settings.OPENAI_API_KEY else '✗ Missing'}")
        print(f"   • Alpaca: {'✓ Configured' if settings.ALPACA_API_KEY else '✗ Missing'}")
        print(f"   • FMP: {'✓ Configured' if settings.FMP_API_KEY else '✗ Missing'}")
        
        print(f"\n🎯 Features:")
        print(f"   • Voice Alerts: {'✓ Enabled' if settings.ENABLE_VOICE_ALERTS else '✗ Disabled'}")
        print(f"   • Desktop Notifications: {'✓ Enabled' if settings.ENABLE_DESKTOP_NOTIFICATIONS else '✗ Disabled'}")
        print(f"   • Paper Trading: ✓ Enabled (Safe Mode)")
        
    except Exception as e:
        print(f"Error getting status: {e}")
    
    print("="*60)

def run_desktop_app():
    """Run the desktop application"""
    try:
        logger.info("Starting desktop application...")
        from desktop_app import main as desktop_main
        desktop_main()
    except Exception as e:
        logger.error(f"Error running desktop app: {e}")

def main():
    parser = argparse.ArgumentParser(description="Autonomous Trading Bot")
    parser.add_argument('command', nargs='?', default='help',
                       choices=['desktop', 'bot', 'dashboard', 'both', 'install', 'setup', 'test', 'status', 'help'],
                       help='Command to run')

    args = parser.parse_args()

    if args.command == 'help':
        print("\n🤖 Autonomous Trading Bot Commands:")
        print("  desktop   - Run the desktop application (RECOMMENDED)")
        print("  bot       - Run the trading bot only (console)")
        print("  dashboard - Run the web dashboard only")
        print("  both      - Run both bot and dashboard")
        print("  install   - Install dependencies")
        print("  setup     - Setup environment and validate config")
        print("  test      - Test API connections")
        print("  status    - Show system status")
        print("  help      - Show this help message")
        print("\nExamples:")
        print("  python run_bot.py desktop    # Start desktop app")
        print("  python run_bot.py setup")
        print("  python run_bot.py test")
        return
    
    if args.command == 'install':
        install_dependencies()
    
    elif args.command == 'setup':
        if install_dependencies():
            setup_environment()
    
    elif args.command == 'test':
        test_connections()
    
    elif args.command == 'status':
        show_status()
    
    elif args.command == 'bot':
        if setup_environment():
            run_main_bot()
    
    elif args.command == 'dashboard':
        run_dashboard()
    
    elif args.command == 'desktop':
        if setup_environment():
            run_desktop_app()

    elif args.command == 'both':
        if setup_environment():
            run_both()

if __name__ == "__main__":
    main()
