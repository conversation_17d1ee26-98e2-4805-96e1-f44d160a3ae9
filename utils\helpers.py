import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Union
import asyncio
import json

logger = logging.getLogger(__name__)

def calculate_returns(prices: List[float]) -> List[float]:
    """Calculate percentage returns from price series"""
    if len(prices) < 2:
        return []
    
    returns = []
    for i in range(1, len(prices)):
        ret = (prices[i] - prices[i-1]) / prices[i-1]
        returns.append(ret)
    
    return returns

def calculate_volatility(returns: List[float], periods: int = 252) -> float:
    """Calculate annualized volatility"""
    if len(returns) < 2:
        return 0.0
    
    return np.std(returns) * np.sqrt(periods)

def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
    """Calculate Sharpe ratio"""
    if len(returns) < 2:
        return 0.0
    
    excess_returns = [r - risk_free_rate/252 for r in returns]  # Daily risk-free rate
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)

def calculate_max_drawdown(prices: List[float]) -> Dict[str, float]:
    """Calculate maximum drawdown"""
    if len(prices) < 2:
        return {"max_drawdown": 0.0, "drawdown_duration": 0}
    
    peak = prices[0]
    max_drawdown = 0.0
    drawdown_start = 0
    max_drawdown_duration = 0
    current_drawdown_duration = 0
    
    for i, price in enumerate(prices):
        if price > peak:
            peak = price
            if current_drawdown_duration > max_drawdown_duration:
                max_drawdown_duration = current_drawdown_duration
            current_drawdown_duration = 0
        else:
            drawdown = (peak - price) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
            current_drawdown_duration += 1
    
    return {
        "max_drawdown": max_drawdown,
        "drawdown_duration": max_drawdown_duration
    }

def format_currency(amount: float) -> str:
    """Format number as currency"""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format number as percentage"""
    return f"{value*100:.2f}%"

def is_market_hours() -> bool:
    """Check if current time is within market hours (9:30 AM - 4:00 PM ET)"""
    now = datetime.now()
    market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
    
    # Check if it's a weekday
    if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return False
    
    return market_open <= now <= market_close

def get_trading_days(start_date: datetime, end_date: datetime) -> List[datetime]:
    """Get list of trading days between two dates (excludes weekends)"""
    trading_days = []
    current_date = start_date
    
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday = 0, Friday = 4
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    return trading_days

def calculate_position_size(account_balance: float, risk_per_trade: float, 
                          entry_price: float, stop_loss_price: float) -> int:
    """Calculate position size based on risk management"""
    if entry_price <= 0 or stop_loss_price <= 0:
        return 0
    
    risk_amount = account_balance * risk_per_trade
    price_risk = abs(entry_price - stop_loss_price)
    
    if price_risk == 0:
        return 0
    
    position_size = int(risk_amount / price_risk)
    return max(0, position_size)

def validate_symbol(symbol: str) -> bool:
    """Validate stock symbol format"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic validation - alphanumeric, 1-5 characters
    symbol = symbol.upper().strip()
    return symbol.isalpha() and 1 <= len(symbol) <= 5

def clean_price_data(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and validate price data"""
    if df.empty:
        return df
    
    # Remove rows with missing or invalid data
    df = df.dropna()
    
    # Remove rows where prices are zero or negative
    numeric_columns = ['Open', 'High', 'Low', 'Close']
    for col in numeric_columns:
        if col in df.columns:
            df = df[df[col] > 0]
    
    # Ensure High >= Low
    if 'High' in df.columns and 'Low' in df.columns:
        df = df[df['High'] >= df['Low']]
    
    # Sort by date
    if df.index.name == 'Date' or 'Date' in df.columns:
        df = df.sort_index() if df.index.name == 'Date' else df.sort_values('Date')
    
    return df

def calculate_technical_levels(prices: List[float], window: int = 20) -> Dict[str, float]:
    """Calculate support and resistance levels"""
    if len(prices) < window:
        return {"support": 0.0, "resistance": 0.0}
    
    # Simple approach: use recent highs and lows
    recent_prices = prices[-window:]
    support = min(recent_prices)
    resistance = max(recent_prices)
    
    return {
        "support": support,
        "resistance": resistance
    }

def risk_reward_ratio(entry_price: float, stop_loss: float, take_profit: float) -> float:
    """Calculate risk-reward ratio"""
    if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
        return 0.0
    
    risk = abs(entry_price - stop_loss)
    reward = abs(take_profit - entry_price)
    
    if risk == 0:
        return 0.0
    
    return reward / risk

def log_trade_performance(trades: List[Dict]) -> Dict:
    """Calculate trading performance metrics"""
    if not trades:
        return {}
    
    total_trades = len(trades)
    winning_trades = [t for t in trades if t.get('profit_loss', 0) > 0]
    losing_trades = [t for t in trades if t.get('profit_loss', 0) < 0]
    
    win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
    
    total_profit = sum(t.get('profit_loss', 0) for t in winning_trades)
    total_loss = sum(abs(t.get('profit_loss', 0)) for t in losing_trades)
    
    avg_win = total_profit / len(winning_trades) if winning_trades else 0
    avg_loss = total_loss / len(losing_trades) if losing_trades else 0
    
    profit_factor = total_profit / total_loss if total_loss > 0 else 0
    
    return {
        "total_trades": total_trades,
        "winning_trades": len(winning_trades),
        "losing_trades": len(losing_trades),
        "win_rate": win_rate,
        "avg_win": avg_win,
        "avg_loss": avg_loss,
        "profit_factor": profit_factor,
        "total_pnl": sum(t.get('profit_loss', 0) for t in trades)
    }

async def retry_async_operation(operation, max_retries: int = 3, delay: float = 1.0):
    """Retry an async operation with exponential backoff"""
    for attempt in range(max_retries):
        try:
            return await operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            wait_time = delay * (2 ** attempt)
            logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries}), retrying in {wait_time}s: {e}")
            await asyncio.sleep(wait_time)

def save_to_json(data: Dict, filename: str):
    """Save data to JSON file"""
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        logger.info(f"Data saved to {filename}")
    except Exception as e:
        logger.error(f"Error saving to {filename}: {e}")

def load_from_json(filename: str) -> Dict:
    """Load data from JSON file"""
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
        logger.info(f"Data loaded from {filename}")
        return data
    except Exception as e:
        logger.error(f"Error loading from {filename}: {e}")
        return {}

def format_time_elapsed(start_time: datetime) -> str:
    """Format elapsed time since start_time"""
    elapsed = datetime.now() - start_time
    
    if elapsed.days > 0:
        return f"{elapsed.days}d {elapsed.seconds//3600}h {(elapsed.seconds%3600)//60}m"
    elif elapsed.seconds >= 3600:
        return f"{elapsed.seconds//3600}h {(elapsed.seconds%3600)//60}m"
    elif elapsed.seconds >= 60:
        return f"{elapsed.seconds//60}m {elapsed.seconds%60}s"
    else:
        return f"{elapsed.seconds}s"
