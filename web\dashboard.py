import dash
from dash import dcc, html, Input, Output, State, dash_table
import dash_bootstrap_components as dbc
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import asyncio
from datetime import datetime, timedelta
import logging

# Import our modules
from config.settings import settings
from database.models import get_db, Stock, Trade, Pattern, Alert
from trading.broker_interface import AlpacaBroker
from data.market_data import MarketDataProvider

logger = logging.getLogger(__name__)

# Initialize Dash app
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
app.title = "Trading Bot Dashboard"

# Global variables
broker = AlpacaBroker()
market_data = MarketDataProvider()

# Layout
app.layout = dbc.Container([
    dbc.Row([
        dbc.Col([
            html.H1("🤖 Autonomous Trading Bot Dashboard", className="text-center mb-4"),
            html.Hr()
        ])
    ]),
    
    # Status Cards
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Account Status", className="card-title"),
                    html.H2(id="account-balance", children="$0.00", className="text-success"),
                    html.P("Available Balance", className="card-text")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Daily P&L", className="card-title"),
                    html.H2(id="daily-pnl", children="$0.00", className="text-info"),
                    html.P("Today's Performance", className="card-text")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Active Positions", className="card-title"),
                    html.H2(id="position-count", children="0", className="text-warning"),
                    html.P("Open Positions", className="card-text")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Patterns Detected", className="card-title"),
                    html.H2(id="pattern-count", children="0", className="text-primary"),
                    html.P("Today's Patterns", className="card-text")
                ])
            ])
        ], width=3)
    ], className="mb-4"),
    
    # Market Overview
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Market Overview"),
                dbc.CardBody([
                    dcc.Graph(id="market-overview-chart")
                ])
            ])
        ], width=8),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Market Movers"),
                dbc.CardBody([
                    html.Div(id="market-movers-list")
                ])
            ])
        ], width=4)
    ], className="mb-4"),
    
    # Patterns and Alerts
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Recent Patterns Detected"),
                dbc.CardBody([
                    dash_table.DataTable(
                        id="patterns-table",
                        columns=[
                            {"name": "Time", "id": "time"},
                            {"name": "Symbol", "id": "symbol"},
                            {"name": "Pattern", "id": "pattern_type"},
                            {"name": "Confidence", "id": "confidence"},
                            {"name": "Action", "id": "action"},
                            {"name": "Price", "id": "price"}
                        ],
                        data=[],
                        style_cell={'textAlign': 'left'},
                        style_data_conditional=[
                            {
                                'if': {'filter_query': '{action} = BUY'},
                                'backgroundColor': '#d4edda',
                                'color': 'black',
                            },
                            {
                                'if': {'filter_query': '{action} = SELL'},
                                'backgroundColor': '#f8d7da',
                                'color': 'black',
                            }
                        ]
                    )
                ])
            ])
        ], width=12)
    ], className="mb-4"),
    
    # Trades and Positions
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Recent Trades"),
                dbc.CardBody([
                    dash_table.DataTable(
                        id="trades-table",
                        columns=[
                            {"name": "Time", "id": "timestamp"},
                            {"name": "Symbol", "id": "symbol"},
                            {"name": "Side", "id": "side"},
                            {"name": "Quantity", "id": "quantity"},
                            {"name": "Price", "id": "price"},
                            {"name": "Status", "id": "status"},
                            {"name": "P&L", "id": "profit_loss"}
                        ],
                        data=[],
                        style_cell={'textAlign': 'left'}
                    )
                ])
            ])
        ], width=6),
        
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Current Positions"),
                dbc.CardBody([
                    dash_table.DataTable(
                        id="positions-table",
                        columns=[
                            {"name": "Symbol", "id": "symbol"},
                            {"name": "Quantity", "id": "quantity"},
                            {"name": "Avg Price", "id": "avg_price"},
                            {"name": "Current Price", "id": "current_price"},
                            {"name": "P&L", "id": "unrealized_pl"},
                            {"name": "P&L %", "id": "unrealized_plpc"}
                        ],
                        data=[],
                        style_cell={'textAlign': 'left'},
                        style_data_conditional=[
                            {
                                'if': {'filter_query': '{unrealized_pl} > 0'},
                                'backgroundColor': '#d4edda',
                                'color': 'black',
                            },
                            {
                                'if': {'filter_query': '{unrealized_pl} < 0'},
                                'backgroundColor': '#f8d7da',
                                'color': 'black',
                            }
                        ]
                    )
                ])
            ])
        ], width=6)
    ], className="mb-4"),
    
    # Control Panel
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Bot Controls"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            dbc.Button("Start Bot", id="start-btn", color="success", className="me-2"),
                            dbc.Button("Stop Bot", id="stop-btn", color="danger", className="me-2"),
                            dbc.Button("Emergency Stop", id="emergency-btn", color="warning")
                        ], width=6),
                        dbc.Col([
                            html.P("Bot Status:", className="mb-1"),
                            html.H5(id="bot-status", children="Stopped", className="text-danger")
                        ], width=6)
                    ])
                ])
            ])
        ], width=12)
    ], className="mb-4"),
    
    # Auto-refresh interval
    dcc.Interval(
        id='interval-component',
        interval=30*1000,  # Update every 30 seconds
        n_intervals=0
    )
    
], fluid=True)

# Callbacks
@app.callback(
    [Output('account-balance', 'children'),
     Output('daily-pnl', 'children'),
     Output('position-count', 'children'),
     Output('pattern-count', 'children')],
    [Input('interval-component', 'n_intervals')]
)
def update_status_cards(n):
    try:
        # Get account info
        account_info = broker.get_account_info()
        balance = f"${account_info.get('buying_power', 0):,.2f}"
        
        # Calculate daily P&L (simplified)
        daily_pnl = "$0.00"  # Would calculate from trades
        
        # Get positions count
        positions = broker.get_positions()
        pos_count = len(positions)
        
        # Get pattern count (would query database)
        pattern_count = 0
        
        return balance, daily_pnl, str(pos_count), str(pattern_count)
        
    except Exception as e:
        logger.error(f"Error updating status cards: {e}")
        return "$0.00", "$0.00", "0", "0"

@app.callback(
    Output('market-overview-chart', 'figure'),
    [Input('interval-component', 'n_intervals')]
)
def update_market_chart(n):
    try:
        # Create a simple market overview chart
        # In a real implementation, this would show market indices
        fig = go.Figure()
        
        # Sample data - replace with real market data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        spy_prices = [400 + i + (i % 5) for i in range(len(dates))]
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=spy_prices,
            mode='lines',
            name='SPY',
            line=dict(color='blue')
        ))
        
        fig.update_layout(
            title="Market Overview (SPY)",
            xaxis_title="Date",
            yaxis_title="Price",
            height=400
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error updating market chart: {e}")
        return go.Figure()

@app.callback(
    Output('positions-table', 'data'),
    [Input('interval-component', 'n_intervals')]
)
def update_positions_table(n):
    try:
        positions = broker.get_positions()
        
        # Format positions data for table
        table_data = []
        for pos in positions:
            table_data.append({
                'symbol': pos['symbol'],
                'quantity': pos['quantity'],
                'avg_price': f"${pos['avg_entry_price']:.2f}",
                'current_price': f"${pos['avg_entry_price']:.2f}",  # Would get current price
                'unrealized_pl': f"${pos['unrealized_pl']:.2f}",
                'unrealized_plpc': f"{pos['unrealized_plpc']*100:.2f}%"
            })
        
        return table_data
        
    except Exception as e:
        logger.error(f"Error updating positions table: {e}")
        return []

@app.callback(
    Output('market-movers-list', 'children'),
    [Input('interval-component', 'n_intervals')]
)
def update_market_movers(n):
    try:
        # Sample market movers - replace with real data
        movers = [
            {"symbol": "AAPL", "change": "+2.5%", "price": "$150.25"},
            {"symbol": "TSLA", "change": "-1.8%", "price": "$245.80"},
            {"symbol": "MSFT", "change": "+1.2%", "price": "$380.45"}
        ]
        
        mover_cards = []
        for mover in movers:
            color = "success" if mover["change"].startswith("+") else "danger"
            card = dbc.Card([
                dbc.CardBody([
                    html.H6(mover["symbol"], className="card-title"),
                    html.P(mover["price"], className="mb-1"),
                    html.P(mover["change"], className=f"text-{color} mb-0")
                ])
            ], className="mb-2")
            mover_cards.append(card)
        
        return mover_cards
        
    except Exception as e:
        logger.error(f"Error updating market movers: {e}")
        return []

if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050)
