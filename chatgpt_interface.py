#!/usr/bin/env python3
"""
ChatGPT-Style Trading Bot Interface
Exact replica of ChatGPT's interface for trading conversations
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog
import threading
from datetime import datetime
import yfinance as yf
import requests
import json

# OpenAI API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

class MarketDataProvider:
    """Real-time market data for AI context"""
    
    def get_stock_data(self, symbol):
        """Get current stock data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="5d")
            
            if hist.empty:
                return None
            
            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100
            
            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'current_price': current_price,
                'change': change,
                'change_percent': change_percent,
                'volume': hist['Volume'].iloc[-1],
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector'),
                '52w_high': info.get('fiftyTwoWeekHigh'),
                '52w_low': info.get('fiftyTwoWeekLow')
            }
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
            return None
    
    def get_insider_trades(self, symbol=None):
        """Get recent insider trading data"""
        try:
            if symbol:
                ticker = yf.Ticker(symbol)
                insider_trades = ticker.insider_transactions
                
                if insider_trades is not None and not insider_trades.empty:
                    recent_trades = insider_trades.head(5)
                    trades_data = []
                    for _, trade in recent_trades.iterrows():
                        trades_data.append({
                            'symbol': symbol,
                            'insider': trade.get('Insider', 'Unknown'),
                            'title': trade.get('Title', 'Unknown'),
                            'transaction': trade.get('Transaction', 'Unknown'),
                            'shares': trade.get('Shares', 0),
                            'price': trade.get('Price', 0),
                            'date': trade.get('Date', 'Unknown')
                        })
                    return trades_data
            else:
                # Get insider trades for popular stocks
                popular_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA']
                all_trades = []
                for stock in popular_stocks:
                    trades = self.get_insider_trades(stock)
                    if trades:
                        all_trades.extend(trades[:2])
                return all_trades
        except Exception as e:
            print(f"Error getting insider trades: {e}")
            return []

class TradingAI:
    """AI trading assistant with real market data"""
    
    def __init__(self):
        self.market_data = MarketDataProvider()
        self.conversation_history = []
        
        self.system_prompt = """You are a professional trading and investment advisor with access to real-time market data including current stock prices, insider trading information, and market news.

You provide specific, actionable trading advice based on current market conditions. When users ask about stocks, insider trades, or market data, you analyze the real-time information provided and give detailed insights.

You are direct, professional, and always include specific price levels, risk assessments, and actionable recommendations."""

    def get_ai_response(self, user_message, market_context=None):
        """Get response from OpenAI API"""
        try:
            if market_context:
                enhanced_message = f"""
User Question: {user_message}

Current Market Data:
{market_context}

Please provide a comprehensive analysis based on this real-time data.
"""
            else:
                enhanced_message = user_message
            
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (last 6 messages)
            for msg in self.conversation_history[-6:]:
                messages.append(msg)
            
            messages.append({"role": "user", "content": enhanced_message})
            
            headers = {
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": messages,
                "max_tokens": 1200,
                "temperature": 0.7
            }
            
            response = requests.post(OPENAI_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                return ai_response
            else:
                return f"API Error: {response.status_code}"
                
        except Exception as e:
            return f"Error: {str(e)}"
    
    def extract_symbols(self, message):
        """Extract stock symbols from message"""
        import re
        
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'amazon': 'AMZN', 'nvidia': 'NVDA',
            'meta': 'META', 'netflix': 'NFLX'
        }
        
        message_lower = message.lower()
        symbols = []
        
        for name, symbol in stock_names.items():
            if name in message_lower:
                symbols.append(symbol)
        
        found_symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
        symbols.extend(found_symbols)
        
        return list(set(symbols))
    
    def get_market_context(self, symbols, user_message):
        """Get market context for AI"""
        context = ""
        
        # Check for insider trading questions
        if any(word in user_message.lower() for word in ['insider', 'insider trading', 'insider trades']):
            if symbols:
                for symbol in symbols[:2]:
                    insider_trades = self.market_data.get_insider_trades(symbol)
                    if insider_trades:
                        context += f"\nRecent Insider Trades for {symbol}:\n"
                        for trade in insider_trades[:3]:
                            context += f"- {trade['insider']} ({trade['title']}): {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f} on {trade['date']}\n"
            else:
                insider_trades = self.market_data.get_insider_trades()
                if insider_trades:
                    context += "\nRecent Insider Trades (Popular Stocks):\n"
                    for trade in insider_trades[:5]:
                        context += f"- {trade['symbol']}: {trade['insider']} {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f}\n"
        
        # Get stock data
        if symbols:
            for symbol in symbols[:3]:
                data = self.market_data.get_stock_data(symbol)
                if data:
                    context += f"""
{symbol} ({data['name']}):
- Current Price: ${data['current_price']:.2f}
- Change: {data['change']:+.2f} ({data['change_percent']:+.2f}%)
- Volume: {data['volume']:,}
- Market Cap: ${data['market_cap']/1e9:.1f}B
- 52W Range: ${data['52w_low']:.2f} - ${data['52w_high']:.2f}
"""
        
        return context if context else None

class ChatGPTInterface:
    """Exact ChatGPT-style interface"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ChatGPT - Trading Assistant")
        self.root.geometry("1200x800")
        self.root.configure(bg='#343541')
        
        self.ai = TradingAI()
        self.create_interface()
        
        # Welcome message
        self.add_ai_message("Hello! I'm your AI trading assistant. I have access to real-time market data, insider trading information, and can help you with stock analysis and trading decisions. What would you like to know?")
    
    def create_interface(self):
        """Create ChatGPT-style interface"""
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#343541')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Chat area
        self.chat_frame = tk.Frame(main_frame, bg='#343541')
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Messages container with scrollbar
        self.messages_frame = tk.Frame(self.chat_frame, bg='#343541')
        self.messages_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable canvas for messages
        self.canvas = tk.Canvas(self.messages_frame, bg='#343541', highlightthickness=0)
        self.scrollbar = tk.Scrollbar(self.messages_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#343541')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Input area
        self.input_frame = tk.Frame(main_frame, bg='#40414f', height=80)
        self.input_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        self.input_frame.pack_propagate(False)
        
        # Input container
        input_container = tk.Frame(self.input_frame, bg='#40414f')
        input_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # Text input
        self.text_input = tk.Text(
            input_container,
            height=2,
            bg='#40414f',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            relief=tk.FLAT,
            insertbackground='#ffffff',
            selectbackground='#565869',
            bd=0
        )
        self.text_input.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Send button
        self.send_button = tk.Button(
            input_container,
            text="→",
            command=self.send_message,
            bg='#19c37d',
            fg='#ffffff',
            font=('Segoe UI', 14, 'bold'),
            relief=tk.FLAT,
            width=3,
            height=1,
            cursor='hand2',
            bd=0
        )
        self.send_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Bind events
        self.text_input.bind('<Return>', self.on_enter)
        self.text_input.bind('<Shift-Return>', self.on_shift_enter)
        
        # Focus on input
        self.text_input.focus_set()
    
    def add_user_message(self, message):
        """Add user message bubble"""
        message_frame = tk.Frame(self.scrollable_frame, bg='#343541')
        message_frame.pack(fill=tk.X, pady=(10, 5), padx=50)
        
        # User message bubble
        bubble = tk.Frame(message_frame, bg='#565869', relief=tk.FLAT)
        bubble.pack(anchor='e', padx=(100, 0))
        
        text_label = tk.Label(
            bubble,
            text=message,
            bg='#565869',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wraplength=500,
            justify=tk.LEFT,
            padx=15,
            pady=10
        )
        text_label.pack()
        
        self.scroll_to_bottom()
    
    def add_ai_message(self, message):
        """Add AI message bubble"""
        message_frame = tk.Frame(self.scrollable_frame, bg='#343541')
        message_frame.pack(fill=tk.X, pady=(5, 10), padx=50)
        
        # AI avatar
        avatar_frame = tk.Frame(message_frame, bg='#343541')
        avatar_frame.pack(anchor='w', fill=tk.X)
        
        avatar = tk.Label(
            avatar_frame,
            text="🤖",
            bg='#343541',
            fg='#19c37d',
            font=('Segoe UI', 16),
            width=3
        )
        avatar.pack(side=tk.LEFT, anchor='n', pady=(5, 0))
        
        # AI message bubble
        bubble = tk.Frame(avatar_frame, bg='#444654', relief=tk.FLAT)
        bubble.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 100))
        
        text_label = tk.Label(
            bubble,
            text=message,
            bg='#444654',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wraplength=600,
            justify=tk.LEFT,
            padx=15,
            pady=10
        )
        text_label.pack(anchor='w')
        
        self.scroll_to_bottom()
    
    def add_thinking_message(self):
        """Add thinking indicator"""
        message_frame = tk.Frame(self.scrollable_frame, bg='#343541')
        message_frame.pack(fill=tk.X, pady=(5, 10), padx=50)
        
        avatar_frame = tk.Frame(message_frame, bg='#343541')
        avatar_frame.pack(anchor='w', fill=tk.X)
        
        avatar = tk.Label(
            avatar_frame,
            text="🤖",
            bg='#343541',
            fg='#19c37d',
            font=('Segoe UI', 16),
            width=3
        )
        avatar.pack(side=tk.LEFT, anchor='n', pady=(5, 0))
        
        bubble = tk.Frame(avatar_frame, bg='#444654', relief=tk.FLAT)
        bubble.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 100))
        
        thinking_label = tk.Label(
            bubble,
            text="Analyzing market data...",
            bg='#444654',
            fg='#888888',
            font=('Segoe UI', 11, 'italic'),
            padx=15,
            pady=10
        )
        thinking_label.pack(anchor='w')
        
        self.scroll_to_bottom()
        return message_frame
    
    def remove_thinking_message(self, thinking_frame):
        """Remove thinking indicator"""
        thinking_frame.destroy()
    
    def scroll_to_bottom(self):
        """Scroll to bottom of chat"""
        self.root.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        self.canvas.yview_moveto(1.0)
    
    def send_message(self):
        """Send message to AI"""
        message = self.text_input.get("1.0", tk.END).strip()
        if not message:
            return
        
        # Clear input
        self.text_input.delete("1.0", tk.END)
        
        # Add user message
        self.add_user_message(message)
        
        # Add thinking indicator
        thinking_frame = self.add_thinking_message()
        
        # Process in background
        def get_response():
            try:
                symbols = self.ai.extract_symbols(message)
                market_context = self.ai.get_market_context(symbols, message)
                response = self.ai.get_ai_response(message, market_context)
                
                # Remove thinking and add response
                self.root.after(0, lambda: self.remove_thinking_message(thinking_frame))
                self.root.after(100, lambda: self.add_ai_message(response))
                
            except Exception as e:
                self.root.after(0, lambda: self.remove_thinking_message(thinking_frame))
                self.root.after(100, lambda: self.add_ai_message(f"Error: {str(e)}"))
        
        threading.Thread(target=get_response, daemon=True).start()
    
    def on_enter(self, event):
        """Handle Enter key"""
        self.send_message()
        return "break"
    
    def on_shift_enter(self, event):
        """Handle Shift+Enter for new line"""
        return None
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch ChatGPT-style trading bot"""
    print("🚀 Launching ChatGPT Trading Interface...")
    app = ChatGPTInterface()
    app.run()

if __name__ == "__main__":
    main()
