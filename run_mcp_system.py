#!/usr/bin/env python3
"""
MCP Trading System Launcher
Comprehensive launcher for the professional trading system
"""

import asyncio
import sys
import argparse
import logging
import subprocess
import threading
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_mcp_desktop():
    """Run the MCP-powered desktop application"""
    try:
        logger.info("Starting MCP Desktop Application...")
        from mcp_desktop_app import main as mcp_main
        mcp_main()
    except Exception as e:
        logger.error(f"Error running MCP desktop app: {e}")

def run_mcp_server():
    """Run the Alpaca MCP server"""
    try:
        logger.info("Starting Alpaca MCP Server...")
        subprocess.run([sys.executable, "alpaca_mcp_server.py"], check=True)
    except Exception as e:
        logger.error(f"Error running MCP server: {e}")

def test_mcp_integration():
    """Test MCP integration"""
    try:
        logger.info("Testing MCP Integration...")
        
        # Test MCP server functions
        from alpaca_mcp_server import get_account_info, get_market_clock, get_stock_quote
        
        async def run_tests():
            try:
                # Test account info
                logger.info("Testing account info...")
                account_result = await get_account_info()
                logger.info(f"Account Info: {account_result}")
                
                # Test market clock
                logger.info("Testing market clock...")
                market_result = await get_market_clock()
                logger.info(f"Market Clock: {market_result}")
                
                # Test stock quote
                logger.info("Testing stock quote...")
                quote_result = await get_stock_quote("AAPL")
                logger.info(f"AAPL Quote: {quote_result}")
                
                logger.info("✅ All MCP tests passed!")
                return True
                
            except Exception as e:
                logger.error(f"❌ MCP test failed: {e}")
                return False
        
        # Run async tests
        success = asyncio.run(run_tests())
        return success
        
    except Exception as e:
        logger.error(f"Error testing MCP integration: {e}")
        return False

def test_stddev_atr_scanner():
    """Test the StdDev/ATR scanner"""
    try:
        logger.info("Testing StdDev/ATR Scanner...")
        
        from mcp_integration import mcp_engine
        
        async def test_scanner():
            try:
                # Initialize engine
                success = await mcp_engine.initialize()
                if not success:
                    logger.error("Failed to initialize MCP engine")
                    return False
                
                # Test StdDev/ATR strategy
                result = await mcp_engine.execute_stddev_atr_strategy("AAPL")
                if result:
                    logger.info(f"✅ StdDev/ATR test successful: {result}")
                    return True
                else:
                    logger.warning("No StdDev/ATR signal generated")
                    return True  # This is normal
                    
            except Exception as e:
                logger.error(f"❌ StdDev/ATR test failed: {e}")
                return False
        
        success = asyncio.run(test_scanner())
        return success
        
    except Exception as e:
        logger.error(f"Error testing StdDev/ATR scanner: {e}")
        return False

def show_mcp_status():
    """Show MCP system status"""
    print("\n" + "="*70)
    print("🚀 PROFESSIONAL TRADING SYSTEM - MCP POWERED")
    print("="*70)
    
    print("\n📊 System Components:")
    print("   • Alpaca MCP Server - Enterprise trading infrastructure")
    print("   • TTM Squeeze Strategy - Professional momentum detection")
    print("   • StdDev/ATR Scanner - Custom volatility pattern analysis")
    print("   • AI Integration - OpenAI GPT-4 powered analysis")
    print("   • Risk Management - Professional position sizing & limits")
    print("   • Real-time Alerts - Voice and desktop notifications")
    
    print("\n🔧 MCP Server Features:")
    print("   • Strategy Engine - Custom trading logic execution")
    print("   • Order Manager - Professional order routing")
    print("   • Position Tracker - Real-time P&L and exposure")
    print("   • Data Handler - Live market data integration")
    print("   • Simulation Mode - Paper trading and backtesting")
    
    print("\n🎯 Trading Strategies:")
    print("   • TTM Squeeze - Bollinger Bands + Keltner Channels")
    print("   • StdDev/ATR - Custom volatility breakout detection")
    print("   • Momentum - EMA crossovers and trend following")
    print("   • AI Analysis - GPT-4 powered trade validation")
    
    print("\n🛡️ Safety Features:")
    print("   • Paper Trading Mode - No real money at risk")
    print("   • Position Limits - Max $1,000 per trade")
    print("   • Risk Controls - 2% risk per trade maximum")
    print("   • Daily Limits - Max 10 trades per day")
    print("   • Emergency Stop - Instant halt all operations")
    
    print("\n🔑 API Integration:")
    print("   • Alpaca Trading API - Professional brokerage")
    print("   • OpenAI GPT-4 - AI-powered analysis")
    print("   • Financial Modeling Prep - Market data")
    print("   • Real-time Data Feeds - Live market information")
    
    print("="*70)

def install_mcp_dependencies():
    """Install MCP-specific dependencies"""
    logger.info("Installing MCP dependencies...")
    try:
        # Install required packages
        packages = [
            "alpaca-py>=0.21.0",
            "mcp>=0.1.0",
            "asyncio-mqtt",
            "websockets",
            "aiofiles"
        ]
        
        for package in packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                logger.info(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                logger.warning(f"⚠️ Failed to install {package} - may already be installed")
        
        logger.info("✅ MCP dependencies installation complete")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error installing MCP dependencies: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Professional Trading System - MCP Powered")
    parser.add_argument('command', nargs='?', default='help',
                       choices=['desktop', 'server', 'test', 'scan', 'install', 'status', 'help'],
                       help='Command to run')
    
    args = parser.parse_args()
    
    if args.command == 'help':
        print("\n🚀 Professional Trading System - MCP Powered")
        print("\nCommands:")
        print("  desktop  - Launch MCP-powered desktop application")
        print("  server   - Run Alpaca MCP server")
        print("  test     - Test MCP integration and connections")
        print("  scan     - Test StdDev/ATR pattern scanner")
        print("  install  - Install MCP dependencies")
        print("  status   - Show system status and capabilities")
        print("  help     - Show this help message")
        print("\nExamples:")
        print("  python run_mcp_system.py desktop    # Launch trading GUI")
        print("  python run_mcp_system.py test       # Test all systems")
        print("  python run_mcp_system.py scan       # Test pattern scanner")
        return
    
    if args.command == 'install':
        install_mcp_dependencies()
    
    elif args.command == 'status':
        show_mcp_status()
    
    elif args.command == 'test':
        print("🧪 Testing MCP Integration...")
        success = test_mcp_integration()
        if success:
            print("✅ All tests passed! System ready for trading.")
        else:
            print("❌ Some tests failed. Check logs for details.")
    
    elif args.command == 'scan':
        print("📊 Testing StdDev/ATR Scanner...")
        success = test_stddev_atr_scanner()
        if success:
            print("✅ Scanner test completed successfully!")
        else:
            print("❌ Scanner test failed. Check logs for details.")
    
    elif args.command == 'desktop':
        print("🖥️ Launching MCP Desktop Application...")
        run_mcp_desktop()
    
    elif args.command == 'server':
        print("🔧 Starting Alpaca MCP Server...")
        run_mcp_server()

if __name__ == "__main__":
    main()
