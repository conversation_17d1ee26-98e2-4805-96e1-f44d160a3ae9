import openai
import asyncio
import json
from typing import Dict, List, Optional
from datetime import datetime
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

class OpenAIAnalyst:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def analyze_market_sentiment(self, news_data: List[Dict]) -> Dict:
        """Analyze market sentiment from news data"""
        try:
            if not news_data:
                return {"sentiment": "neutral", "confidence": 0.5, "summary": "No news data available"}
            
            # Prepare news summary for analysis
            news_text = ""
            for article in news_data[:10]:  # Analyze top 10 articles
                title = article.get('title', '')
                text = article.get('text', article.get('summary', ''))[:200]  # First 200 chars
                news_text += f"Title: {title}\nSummary: {text}\n\n"
            
            prompt = f"""
            Analyze the following market news and provide sentiment analysis:
            
            {news_text}
            
            Please provide:
            1. Overall market sentiment (bullish, bearish, neutral)
            2. Confidence level (0-1)
            3. Key themes or concerns
            4. Brief summary (2-3 sentences)
            
            Respond in JSON format:
            {{
                "sentiment": "bullish/bearish/neutral",
                "confidence": 0.0-1.0,
                "themes": ["theme1", "theme2"],
                "summary": "brief summary"
            }}
            """
            
            response = await self._make_openai_request(prompt)
            
            try:
                analysis = json.loads(response)
                return analysis
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "sentiment": "neutral",
                    "confidence": 0.5,
                    "themes": ["analysis_error"],
                    "summary": response[:200]
                }
                
        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return {"sentiment": "neutral", "confidence": 0.5, "summary": "Analysis failed"}

    async def analyze_stock_pattern(self, symbol: str, pattern_data: Dict, 
                                  historical_data: Dict) -> Dict:
        """Get AI analysis of detected stock pattern"""
        try:
            prompt = f"""
            Analyze this trading pattern for {symbol}:
            
            Pattern Details:
            - Type: {pattern_data.get('pattern_type', 'Unknown')}
            - Confidence: {pattern_data.get('confidence', 0)*100:.1f}%
            - Current Price: ${pattern_data.get('price', 0):.2f}
            - Volume: {pattern_data.get('volume', 0):,}
            - Description: {pattern_data.get('description', '')}
            - Suggested Action: {pattern_data.get('action', 'WATCH')}
            
            Recent Performance:
            - 52-week high: ${historical_data.get('high_52w', 0):.2f}
            - 52-week low: ${historical_data.get('low_52w', 0):.2f}
            - Average volume: {historical_data.get('avg_volume', 0):,}
            - Market cap: ${historical_data.get('market_cap', 0):,}
            
            Please provide:
            1. Risk assessment (low/medium/high)
            2. Probability of success (0-100%)
            3. Recommended position size (small/medium/large)
            4. Key risks to watch
            5. Price targets (if applicable)
            6. Overall recommendation
            
            Respond in JSON format:
            {{
                "risk_level": "low/medium/high",
                "success_probability": 0-100,
                "position_size": "small/medium/large",
                "risks": ["risk1", "risk2"],
                "price_targets": {{"support": 0.0, "resistance": 0.0}},
                "recommendation": "buy/sell/hold/watch",
                "reasoning": "brief explanation"
            }}
            """
            
            response = await self._make_openai_request(prompt)
            
            try:
                analysis = json.loads(response)
                return analysis
            except json.JSONDecodeError:
                return {
                    "risk_level": "medium",
                    "success_probability": 50,
                    "position_size": "small",
                    "recommendation": "watch",
                    "reasoning": "Analysis parsing failed"
                }
                
        except Exception as e:
            logger.error(f"Error analyzing stock pattern for {symbol}: {e}")
            return {"recommendation": "watch", "reasoning": "Analysis failed"}

    async def generate_trading_strategy(self, market_data: Dict, 
                                      patterns: List[Dict]) -> Dict:
        """Generate overall trading strategy based on market conditions"""
        try:
            patterns_summary = ""
            for pattern in patterns[:5]:  # Top 5 patterns
                patterns_summary += f"- {pattern.get('symbol')}: {pattern.get('pattern_type')} (confidence: {pattern.get('confidence', 0)*100:.0f}%)\n"
            
            prompt = f"""
            Based on current market conditions and detected patterns, generate a trading strategy:
            
            Market Overview:
            - Market sentiment: {market_data.get('sentiment', 'neutral')}
            - VIX level: {market_data.get('vix', 'unknown')}
            - Major indices trend: {market_data.get('indices_trend', 'unknown')}
            
            Top Detected Patterns:
            {patterns_summary}
            
            Account Status:
            - Available buying power: ${market_data.get('buying_power', 0):,.2f}
            - Current positions: {market_data.get('position_count', 0)}
            - Day trades used: {market_data.get('day_trades', 0)}
            
            Please provide:
            1. Overall market outlook (bullish/bearish/neutral)
            2. Recommended trading approach (aggressive/moderate/conservative)
            3. Top 3 symbols to focus on
            4. Risk management advice
            5. Position sizing recommendations
            6. Market timing considerations
            
            Respond in JSON format:
            {{
                "market_outlook": "bullish/bearish/neutral",
                "trading_approach": "aggressive/moderate/conservative",
                "focus_symbols": ["SYM1", "SYM2", "SYM3"],
                "risk_management": "advice text",
                "position_sizing": "advice text",
                "timing": "advice text",
                "key_levels": {{"support": 0.0, "resistance": 0.0}}
            }}
            """
            
            response = await self._make_openai_request(prompt)
            
            try:
                strategy = json.loads(response)
                return strategy
            except json.JSONDecodeError:
                return {
                    "market_outlook": "neutral",
                    "trading_approach": "conservative",
                    "focus_symbols": [],
                    "risk_management": "Use proper position sizing and stop losses"
                }
                
        except Exception as e:
            logger.error(f"Error generating trading strategy: {e}")
            return {"trading_approach": "conservative"}

    async def analyze_earnings_impact(self, symbol: str, earnings_data: Dict) -> Dict:
        """Analyze potential impact of earnings on stock price"""
        try:
            prompt = f"""
            Analyze the potential impact of upcoming earnings for {symbol}:
            
            Earnings Data:
            - Date: {earnings_data.get('date', 'Unknown')}
            - Expected EPS: ${earnings_data.get('eps_estimate', 'N/A')}
            - Revenue estimate: ${earnings_data.get('revenue_estimate', 'N/A')}
            - Previous quarter performance: {earnings_data.get('previous_performance', 'N/A')}
            
            Please assess:
            1. Earnings surprise probability (0-100%)
            2. Potential price movement range
            3. Key metrics to watch
            4. Pre/post earnings strategy
            5. Risk factors
            
            Respond in JSON format:
            {{
                "surprise_probability": 0-100,
                "price_movement_range": {{"low": -0.0, "high": 0.0}},
                "key_metrics": ["metric1", "metric2"],
                "strategy": "hold/buy/sell/avoid",
                "risk_factors": ["risk1", "risk2"]
            }}
            """
            
            response = await self._make_openai_request(prompt)
            
            try:
                analysis = json.loads(response)
                return analysis
            except json.JSONDecodeError:
                return {
                    "surprise_probability": 50,
                    "strategy": "hold",
                    "risk_factors": ["earnings_volatility"]
                }
                
        except Exception as e:
            logger.error(f"Error analyzing earnings impact for {symbol}: {e}")
            return {"strategy": "hold"}

    async def _make_openai_request(self, prompt: str) -> str:
        """Make request to OpenAI API"""
        try:
            response = self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert financial analyst and trading advisor. Provide accurate, data-driven analysis while being mindful of risk management. Always respond in the requested JSON format when specified."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error making OpenAI request: {e}")
            return "Analysis unavailable due to API error"

# Global AI analyst instance
ai_analyst = OpenAIAnalyst()
